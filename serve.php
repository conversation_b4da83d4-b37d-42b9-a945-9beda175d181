<?php
/**
 * Development Server for ATV Gallery
 * Quick way to test the application locally
 */

if (php_sapi_name() !== 'cli') {
    die('This script must be run from the command line.');
}

$host = $argv[1] ?? 'localhost';
$port = $argv[2] ?? '8000';

echo "Starting ATV Gallery development server...\n";
echo "Server: http://$host:$port\n";
echo "Document root: " . __DIR__ . "/public\n";
echo "Press Ctrl+C to stop the server\n\n";

// Change to public directory
chdir(__DIR__ . '/public');

// Start PHP built-in server
$command = "php -S $host:$port";
passthru($command);
?>
