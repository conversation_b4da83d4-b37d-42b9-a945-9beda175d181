<?php

namespace App\Controllers;

use App\Models\Category;
use App\Models\Image;
use App\Services\ImageService;

class AdminController {
    private $categoryModel;
    private $imageModel;
    private $imageService;
    
    public function __construct() {
        $this->categoryModel = new Category();
        $this->imageModel = new Image();
        $this->imageService = new ImageService();
    }
    
    public function dashboard() {
        $stats = [
            'total_images' => $this->getTotalImages(),
            'total_categories' => $this->getTotalCategories(),
            'recent_images' => $this->getRecentImages(5)
        ];
        
        view('admin/dashboard', $stats);
    }
    
    public function categories() {
        $action = $_GET['action'] ?? 'list';
        
        switch ($action) {
            case 'create':
                $this->createCategory();
                break;
            case 'edit':
                $this->editCategory();
                break;
            case 'delete':
                $this->deleteCategory();
                break;
            default:
                $this->listCategories();
                break;
        }
    }
    
    public function images() {
        $action = $_GET['action'] ?? 'list';
        
        switch ($action) {
            case 'edit':
                $this->editImage();
                break;
            case 'delete':
                $this->deleteImage();
                break;
            default:
                $this->listImages();
                break;
        }
    }
    
    public function showUpload() {
        $categories = $this->categoryModel->all('name', 'ASC');
        view('admin/upload', ['categories' => $categories]);
    }
    
    public function uploadImages() {
        if (!isset($_FILES['images'])) {
            $_SESSION['error'] = 'لم يتم اختيار أي صور';
            redirect('/admin/upload');
        }
        
        $files = $_FILES['images'];
        $categoryIds = $_POST['categories'] ?? [];
        $uploadedCount = 0;
        $errors = [];
        
        // Handle multiple files
        if (is_array($files['name'])) {
            for ($i = 0; $i < count($files['name']); $i++) {
                if ($files['error'][$i] === UPLOAD_ERR_OK) {
                    $file = [
                        'name' => $files['name'][$i],
                        'type' => $files['type'][$i],
                        'tmp_name' => $files['tmp_name'][$i],
                        'error' => $files['error'][$i],
                        'size' => $files['size'][$i]
                    ];
                    
                    $result = $this->processImageUpload($file, $categoryIds);
                    if ($result['success']) {
                        $uploadedCount++;
                    } else {
                        $errors[] = $files['name'][$i] . ': ' . $result['error'];
                    }
                }
            }
        } else {
            // Single file
            $result = $this->processImageUpload($files, $categoryIds);
            if ($result['success']) {
                $uploadedCount++;
            } else {
                $errors[] = $result['error'];
            }
        }
        
        if ($uploadedCount > 0) {
            $_SESSION['success'] = "تم رفع {$uploadedCount} صورة بنجاح";
        }
        
        if (!empty($errors)) {
            $_SESSION['error'] = implode('<br>', $errors);
        }
        
        redirect('/admin/images');
    }
    
    private function processImageUpload($file, $categoryIds) {
        $result = $this->imageService->processUpload($file);
        
        if ($result['success']) {
            // Create image record
            $imageData = [
                'title' => pathinfo($file['name'], PATHINFO_FILENAME),
                'description' => '',
                'file_path' => $result['filename'],
                'thumb_path' => $result['thumb_filename'],
                'alt_text' => pathinfo($file['name'], PATHINFO_FILENAME),
                'meta' => $result['meta']
            ];
            
            $image = $this->imageModel->create($imageData);
            
            if ($image && !empty($categoryIds)) {
                $this->imageModel->syncCategories($image['id'], $categoryIds);
            }
        }
        
        return $result;
    }
    
    private function listCategories() {
        $categories = $this->categoryModel->all('name', 'ASC');
        
        // Add image count for each category
        foreach ($categories as &$category) {
            $category['image_count'] = $this->categoryModel->getImageCount($category['id']);
        }
        
        view('admin/categories/list', ['categories' => $categories]);
    }
    
    private function createCategory() {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $data = [
                'name' => trim($_POST['name'] ?? ''),
                'description' => trim($_POST['description'] ?? '')
            ];
            
            if (empty($data['name'])) {
                $_SESSION['error'] = 'اسم الصنف مطلوب';
            } else {
                $category = $this->categoryModel->create($data);
                if ($category) {
                    $_SESSION['success'] = 'تم إنشاء الصنف بنجاح';
                    redirect('/admin/categories');
                } else {
                    $_SESSION['error'] = 'فشل في إنشاء الصنف';
                }
            }
        }
        
        view('admin/categories/create');
    }
    
    private function editCategory() {
        $id = $_GET['id'] ?? 0;
        $category = $this->categoryModel->find($id);
        
        if (!$category) {
            $_SESSION['error'] = 'الصنف غير موجود';
            redirect('/admin/categories');
        }
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $data = [
                'name' => trim($_POST['name'] ?? ''),
                'description' => trim($_POST['description'] ?? '')
            ];
            
            if (empty($data['name'])) {
                $_SESSION['error'] = 'اسم الصنف مطلوب';
            } else {
                $updated = $this->categoryModel->update($id, $data);
                if ($updated) {
                    $_SESSION['success'] = 'تم تحديث الصنف بنجاح';
                    redirect('/admin/categories');
                } else {
                    $_SESSION['error'] = 'فشل في تحديث الصنف';
                }
            }
        }
        
        view('admin/categories/edit', ['category' => $category]);
    }
    
    private function deleteCategory() {
        $id = $_GET['id'] ?? 0;
        
        if ($this->categoryModel->delete($id)) {
            $_SESSION['success'] = 'تم حذف الصنف بنجاح';
        } else {
            $_SESSION['error'] = 'فشل في حذف الصنف';
        }
        
        redirect('/admin/categories');
    }
    
    private function listImages() {
        $page = $_GET['page'] ?? 1;
        $search = $_GET['search'] ?? '';
        $categoryId = $_GET['category'] ?? '';
        
        if (!empty($search)) {
            $result = $this->imageModel->search($search, $page, 12);
        } elseif (!empty($categoryId)) {
            $result = $this->categoryModel->getImages($categoryId, $page, 12);
        } else {
            $result = $this->imageModel->paginate($page, 12);
        }
        
        $categories = $this->categoryModel->all('name', 'ASC');
        
        view('admin/images/list', [
            'images' => $result,
            'categories' => $categories,
            'search' => $search,
            'selectedCategory' => $categoryId,
            'imageModel' => $this->imageModel
        ]);
    }
    
    private function editImage() {
        $id = $_GET['id'] ?? 0;
        $image = $this->imageModel->find($id);
        
        if (!$image) {
            $_SESSION['error'] = 'الصورة غير موجودة';
            redirect('/admin/images');
        }
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $data = [
                'title' => trim($_POST['title'] ?? ''),
                'description' => trim($_POST['description'] ?? ''),
                'alt_text' => trim($_POST['alt_text'] ?? '')
            ];
            
            $categoryIds = $_POST['categories'] ?? [];
            
            if (empty($data['title'])) {
                $_SESSION['error'] = 'عنوان الصورة مطلوب';
            } else {
                $updated = $this->imageModel->update($id, $data);
                if ($updated) {
                    $this->imageModel->syncCategories($id, $categoryIds);
                    $_SESSION['success'] = 'تم تحديث الصورة بنجاح';
                    redirect('/admin/images');
                } else {
                    $_SESSION['error'] = 'فشل في تحديث الصورة';
                }
            }
        }
        
        $categories = $this->categoryModel->all('name', 'ASC');
        $imageCategories = $this->imageModel->getCategories($id);
        $imageCategoryIds = array_column($imageCategories, 'id');
        
        view('admin/images/edit', [
            'image' => $image,
            'categories' => $categories,
            'imageCategoryIds' => $imageCategoryIds
        ]);
    }
    
    private function deleteImage() {
        $id = $_GET['id'] ?? 0;
        
        if ($this->imageModel->delete($id)) {
            $_SESSION['success'] = 'تم حذف الصورة بنجاح';
        } else {
            $_SESSION['error'] = 'فشل في حذف الصورة';
        }
        
        redirect('/admin/images');
    }
    
    private function getTotalImages() {
        $pdo = \Database::getConnection();
        $stmt = $pdo->query("SELECT COUNT(*) FROM images");
        return $stmt->fetchColumn();
    }

    private function getTotalCategories() {
        $pdo = \Database::getConnection();
        $stmt = $pdo->query("SELECT COUNT(*) FROM categories");
        return $stmt->fetchColumn();
    }
    
    private function getRecentImages($limit = 5) {
        return $this->imageModel->paginate(1, $limit)['data'];
    }
}
