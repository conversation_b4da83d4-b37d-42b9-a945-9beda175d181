<?php

namespace App\Controllers;

use App\Models\Category;
use App\Models\Image;

class GalleryController {
    private $categoryModel;
    private $imageModel;
    
    public function __construct() {
        $this->categoryModel = new Category();
        $this->imageModel = new Image();
    }
    
    public function index() {
        $page = $_GET['page'] ?? 1;
        $search = $_GET['search'] ?? '';
        $categorySlug = $_GET['category'] ?? '';
        
        $selectedCategory = null;
        if (!empty($categorySlug)) {
            $selectedCategory = $this->categoryModel->findBySlug($categorySlug);
        }
        
        // Get images based on filters
        if (!empty($search)) {
            $images = $this->imageModel->search($search, $page, 12);
        } elseif ($selectedCategory) {
            $images = $this->categoryModel->getImages($selectedCategory['id'], $page, 12);
        } else {
            $images = $this->imageModel->paginate($page, 12);
        }
        
        // Get all categories for filter
        $categories = $this->categoryModel->all('name', 'ASC');
        
        // Add image count for each category
        foreach ($categories as &$category) {
            $category['image_count'] = $this->categoryModel->getImageCount($category['id']);
        }
        
        view('public/gallery', [
            'images' => $images,
            'categories' => $categories,
            'selectedCategory' => $selectedCategory,
            'search' => $search,
            'currentPage' => 'gallery'
        ]);
    }
    
    public function category() {
        $slug = $_GET['slug'] ?? '';
        
        if (empty($slug)) {
            redirect('/');
        }
        
        $category = $this->categoryModel->findBySlug($slug);
        
        if (!$category) {
            http_response_code(404);
            view('public/404');
            return;
        }
        
        $page = $_GET['page'] ?? 1;
        $images = $this->categoryModel->getImages($category['id'], $page, 12);
        
        // Get all categories for navigation
        $categories = $this->categoryModel->all('name', 'ASC');
        
        view('public/category', [
            'category' => $category,
            'images' => $images,
            'categories' => $categories,
            'currentPage' => 'category'
        ]);
    }
}
