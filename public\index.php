<?php
require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../src/config.php';

use App\Controllers\GalleryController;
use App\Controllers\AdminController;
use App\Controllers\AuthController;
use App\Middleware\AdminMiddleware;

// Start session
session_start();

// Get the request URI and method
$requestUri = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
$requestMethod = $_SERVER['REQUEST_METHOD'];

// Remove base path if needed
$basePath = '/atv_galary/public';
if (strpos($requestUri, $basePath) === 0) {
    $requestUri = substr($requestUri, strlen($basePath));
}

// Simple routing
switch ($requestUri) {
    case '/':
    case '':
        $controller = new GalleryController();
        $controller->index();
        break;
        
    case (preg_match('/^\/category\/(.+)$/', $requestUri, $matches) ? true : false):
        $_GET['slug'] = $matches[1];
        $controller = new GalleryController();
        $controller->category();
        break;
        
    case '/login':
        $controller = new AuthController();
        if ($requestMethod === 'POST') {
            $controller->login();
        } else {
            $controller->showLogin();
        }
        break;
        
    case '/logout':
        $controller = new AuthController();
        $controller->logout();
        break;
        
    case '/admin':
        AdminMiddleware::check();
        $controller = new AdminController();
        $controller->dashboard();
        break;
        
    case '/admin/categories':
        AdminMiddleware::check();
        $controller = new AdminController();
        $controller->categories();
        break;
        
    case '/admin/images':
        AdminMiddleware::check();
        $controller = new AdminController();
        $controller->images();
        break;
        
    case '/admin/upload':
        AdminMiddleware::check();
        $controller = new AdminController();
        if ($requestMethod === 'POST') {
            $controller->uploadImages();
        } else {
            $controller->showUpload();
        }
        break;
        
    default:
        http_response_code(404);
        echo "Page not found";
        break;
}
