<?php

class DatabaseSeeder {
    private $pdo;
    
    public function __construct() {
        $this->pdo = Database::getConnection();
    }
    
    public function run() {
        echo "Starting database seeding...\n";
        
        $this->seedUsers();
        $this->seedCategories();
        
        echo "Database seeding completed successfully!\n";
    }
    
    private function seedUsers() {
        echo "Seeding users...\n";
        
        // Check if admin user already exists
        $stmt = $this->pdo->prepare("SELECT COUNT(*) FROM users WHERE email = ?");
        $stmt->execute(['<EMAIL>']);
        
        if ($stmt->fetchColumn() == 0) {
            // Create admin user
            $stmt = $this->pdo->prepare("
                INSERT INTO users (email, password, name, is_admin, created_at, updated_at) 
                VALUES (?, ?, ?, ?, NOW(), NOW())
            ");
            
            $hashedPassword = password_hash('admin123', PASSWORD_DEFAULT);
            $stmt->execute([
                '<EMAIL>',
                $hashedPassword,
                'مدير النظام',
                1
            ]);
            
            echo "Admin user created: <EMAIL> / admin123\n";
        } else {
            echo "Admin user already exists.\n";
        }
        
        // Create a regular user for testing
        $stmt = $this->pdo->prepare("SELECT COUNT(*) FROM users WHERE email = ?");
        $stmt->execute(['<EMAIL>']);
        
        if ($stmt->fetchColumn() == 0) {
            $stmt = $this->pdo->prepare("
                INSERT INTO users (email, password, name, is_admin, created_at, updated_at) 
                VALUES (?, ?, ?, ?, NOW(), NOW())
            ");
            
            $hashedPassword = password_hash('user123', PASSWORD_DEFAULT);
            $stmt->execute([
                '<EMAIL>',
                $hashedPassword,
                'مستخدم عادي',
                0
            ]);
            
            echo "Regular user created: <EMAIL> / user123\n";
        } else {
            echo "Regular user already exists.\n";
        }
    }
    
    private function seedCategories() {
        echo "Seeding categories...\n";
        
        $categories = [
            [
                'name' => 'طبيعة',
                'slug' => 'nature',
                'description' => 'صور طبيعية خلابة من مناظر طبيعية وحدائق ومناظر جبلية'
            ],
            [
                'name' => 'مدن',
                'slug' => 'cities',
                'description' => 'صور من المدن والمباني والعمارة الحديثة والتراثية'
            ],
            [
                'name' => 'أشخاص',
                'slug' => 'people',
                'description' => 'صور شخصية وجماعية ولقطات من الحياة اليومية'
            ],
            [
                'name' => 'طعام',
                'slug' => 'food',
                'description' => 'صور للأطعمة والمشروبات والوصفات'
            ],
            [
                'name' => 'تقنية',
                'slug' => 'technology',
                'description' => 'صور للأجهزة التقنية والابتكارات والتطبيقات'
            ],
            [
                'name' => 'رياضة',
                'slug' => 'sports',
                'description' => 'صور رياضية من مختلف الألعاب والأنشطة الرياضية'
            ],
            [
                'name' => 'فن',
                'slug' => 'art',
                'description' => 'أعمال فنية ولوحات ومنحوتات وإبداعات فنية'
            ],
            [
                'name' => 'سفر',
                'slug' => 'travel',
                'description' => 'صور من الرحلات والسفر والمعالم السياحية'
            ]
        ];
        
        foreach ($categories as $category) {
            // Check if category already exists
            $stmt = $this->pdo->prepare("SELECT COUNT(*) FROM categories WHERE slug = ?");
            $stmt->execute([$category['slug']]);
            
            if ($stmt->fetchColumn() == 0) {
                $stmt = $this->pdo->prepare("
                    INSERT INTO categories (name, slug, description, created_at, updated_at) 
                    VALUES (?, ?, ?, NOW(), NOW())
                ");
                
                $stmt->execute([
                    $category['name'],
                    $category['slug'],
                    $category['description']
                ]);
                
                echo "Category created: {$category['name']}\n";
            } else {
                echo "Category already exists: {$category['name']}\n";
            }
        }
    }
}
