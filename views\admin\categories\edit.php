<?php
$content = ob_start();
?>

<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-edit me-2"></i>تعديل الصنف: <?= htmlspecialchars($category['name']) ?>
        </h5>
    </div>
    <div class="card-body">
        <form method="POST" action="/admin/categories?action=edit&id=<?= $category['id'] ?>">
            <div class="row">
                <div class="col-md-8">
                    <div class="mb-3">
                        <label for="name" class="form-label">اسم الصنف <span class="text-danger">*</span></label>
                        <input type="text" 
                               class="form-control" 
                               id="name" 
                               name="name" 
                               value="<?= htmlspecialchars($category['name']) ?>"
                               required
                               placeholder="مثال: طبيعة، سيارات، أشخاص">
                        <div class="form-text">الرابط الحالي: <code><?= htmlspecialchars($category['slug']) ?></code></div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">الوصف (اختياري)</label>
                        <textarea class="form-control" 
                                  id="description" 
                                  name="description" 
                                  rows="4"
                                  placeholder="وصف مختصر عن هذا الصنف..."><?= htmlspecialchars($category['description'] ?? '') ?></textarea>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card bg-light">
                        <div class="card-body">
                            <h6 class="card-title">
                                <i class="fas fa-info-circle me-2"></i>معلومات الصنف
                            </h6>
                            <ul class="small mb-3">
                                <li><strong>تاريخ الإنشاء:</strong><br>
                                    <?= date('Y/m/d H:i', strtotime($category['created_at'])) ?>
                                </li>
                                <li><strong>آخر تحديث:</strong><br>
                                    <?= date('Y/m/d H:i', strtotime($category['updated_at'])) ?>
                                </li>
                                <li><strong>الرابط:</strong><br>
                                    <code><?= htmlspecialchars($category['slug']) ?></code>
                                </li>
                            </ul>
                            
                            <div class="d-grid gap-2">
                                <a href="/category/<?= $category['slug'] ?>" 
                                   class="btn btn-sm btn-outline-info" 
                                   target="_blank">
                                    <i class="fas fa-external-link-alt me-1"></i>معاينة
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="d-flex justify-content-between">
                <a href="/admin/categories" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-2"></i>إلغاء
                </a>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save me-2"></i>حفظ التغييرات
                </button>
            </div>
        </form>
    </div>
</div>

<?php
$content = ob_get_clean();

// Include admin layout
$currentPage = 'categories';
$pageTitle = 'تعديل الصنف';
$breadcrumb = [
    ['title' => 'الرئيسية', 'url' => '/admin'],
    ['title' => 'الأصناف', 'url' => '/admin/categories'],
    ['title' => 'تعديل: ' . $category['name']]
];

include VIEWS_PATH . 'layouts/admin.php';
?>
