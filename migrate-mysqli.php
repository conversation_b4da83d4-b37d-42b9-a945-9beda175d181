<?php

echo "Using MySQLi version...\n";

// Load environment variables
if (file_exists('.env')) {
    $envContent = file_get_contents('.env');
    $envLines = explode("\n", $envContent);
    
    foreach ($envLines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value);
        }
    }
}

// Database configuration
$dbHost = $_ENV['DB_HOST'] ?? 'localhost';
$dbPort = $_ENV['DB_PORT'] ?? '3306';
$dbName = $_ENV['DB_DATABASE'] ?? 'atv_gallery';
$dbUser = $_ENV['DB_USERNAME'] ?? 'root';
$dbPass = $_ENV['DB_PASSWORD'] ?? '';

echo "Connecting to database: $dbHost:$dbPort/$dbName\n";

// Connect to MySQL
$mysqli = new mysqli($dbHost, $dbUser, $dbPass, $dbName, $dbPort);

// Check connection
if ($mysqli->connect_error) {
    die("Connection failed: " . $mysqli->connect_error . "\n");
}

// Set charset
$mysqli->set_charset("utf8mb4");

echo "✓ Connected to database successfully\n\n";

class MySQLiMigrator {
    private $mysqli;
    private $migrationsPath;
    
    public function __construct($mysqli) {
        $this->mysqli = $mysqli;
        $this->migrationsPath = __DIR__ . '/database/migrations/';
        $this->createMigrationsTable();
    }
    
    private function createMigrationsTable() {
        $sql = "
            CREATE TABLE IF NOT EXISTS migrations (
                id INT AUTO_INCREMENT PRIMARY KEY,
                migration VARCHAR(255) NOT NULL,
                executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        
        if (!$this->mysqli->query($sql)) {
            die("Error creating migrations table: " . $this->mysqli->error . "\n");
        }
    }
    
    public function migrate() {
        $files = glob($this->migrationsPath . '*.php');
        sort($files);
        
        foreach ($files as $file) {
            $migrationName = basename($file, '.php');
            
            // Check if migration already executed
            $stmt = $this->mysqli->prepare("SELECT COUNT(*) FROM migrations WHERE migration = ?");
            $stmt->bind_param("s", $migrationName);
            $stmt->execute();
            $result = $stmt->get_result();
            $count = $result->fetch_row()[0];
            $stmt->close();
            
            if ($count == 0) {
                echo "Running migration: $migrationName\n";
                
                // Run the migration
                $this->runMigration($migrationName);
                
                // Record migration
                $stmt = $this->mysqli->prepare("INSERT INTO migrations (migration) VALUES (?)");
                $stmt->bind_param("s", $migrationName);
                $stmt->execute();
                $stmt->close();
                
                echo "Migration $migrationName completed.\n";
            } else {
                echo "Migration $migrationName already executed.\n";
            }
        }
        
        echo "All migrations completed.\n";
    }
    
    private function runMigration($migrationName) {
        switch ($migrationName) {
            case '001_create_users_table':
                $this->createUsersTable();
                break;
            case '002_create_categories_table':
                $this->createCategoriesTable();
                break;
            case '003_create_images_table':
                $this->createImagesTable();
                break;
            case '004_create_category_image_table':
                $this->createCategoryImageTable();
                break;
            default:
                echo "Unknown migration: $migrationName\n";
        }
    }
    
    private function createUsersTable() {
        $sql = "
            CREATE TABLE IF NOT EXISTS users (
                id INT AUTO_INCREMENT PRIMARY KEY,
                email VARCHAR(255) NOT NULL UNIQUE,
                password VARCHAR(255) NOT NULL,
                name VARCHAR(255) NOT NULL,
                is_admin BOOLEAN DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        
        if (!$this->mysqli->query($sql)) {
            die("Error creating users table: " . $this->mysqli->error . "\n");
        }
        echo "Users table created successfully.\n";
    }
    
    private function createCategoriesTable() {
        $sql = "
            CREATE TABLE IF NOT EXISTS categories (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                slug VARCHAR(255) NOT NULL UNIQUE,
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_slug (slug)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        
        if (!$this->mysqli->query($sql)) {
            die("Error creating categories table: " . $this->mysqli->error . "\n");
        }
        echo "Categories table created successfully.\n";
    }
    
    private function createImagesTable() {
        $sql = "
            CREATE TABLE IF NOT EXISTS images (
                id INT AUTO_INCREMENT PRIMARY KEY,
                title VARCHAR(255) NOT NULL,
                description TEXT,
                file_path VARCHAR(500) NOT NULL,
                thumb_path VARCHAR(500) NOT NULL,
                alt_text VARCHAR(255),
                meta JSON,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_title (title),
                INDEX idx_created_at (created_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        
        if (!$this->mysqli->query($sql)) {
            die("Error creating images table: " . $this->mysqli->error . "\n");
        }
        echo "Images table created successfully.\n";
    }
    
    private function createCategoryImageTable() {
        $sql = "
            CREATE TABLE IF NOT EXISTS category_image (
                category_id INT NOT NULL,
                image_id INT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (category_id, image_id),
                FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE CASCADE,
                FOREIGN KEY (image_id) REFERENCES images(id) ON DELETE CASCADE,
                INDEX idx_category_id (category_id),
                INDEX idx_image_id (image_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        
        if (!$this->mysqli->query($sql)) {
            die("Error creating category_image table: " . $this->mysqli->error . "\n");
        }
        echo "Category_image pivot table created successfully.\n";
    }
}

// Run migrations
try {
    $migrator = new MySQLiMigrator($mysqli);
    $migrator->migrate();
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    exit(1);
} finally {
    $mysqli->close();
}
