# ATV Gallery - معرض الصور

نظام معرض صور متطور مع لوحة إدارة شاملة، مبني بـ PHP مع دعم كامل للغة العربية و RTL.

## الميزات

### الواجهة العامة
- عرض الصور بتصميم Masonry Grid أنيق
- فلترة الصور حسب الأصناف
- بحث متقدم في العناوين والأوصاف
- Lightbox لعرض الصور بالحجم الكامل
- ترقيم الصفحات (Pagination)
- تصميم متجاوب (Responsive)
- دعم كامل للعربية و RTL

### لوحة الإدارة
- رفع صور متعددة بالسحب والإفلات
- إنشاء وإدارة الأصناف
- ربط الصور بالأصناف
- إنشاء مصغرات تلقائياً
- استخراج بيانات EXIF
- إدارة المستخدمين والصلاحيات
- إحصائيات ولوحة تحكم

## متطلبات النظام

- PHP 8.2+
- MySQL 5.7+ أو MariaDB 10.3+
- Composer
- GD Extension أو Imagick
- mod_rewrite (Apache) أو URL Rewriting (Nginx)

## التثبيت

### 1. تحميل المشروع
```bash
git clone <repository-url> atv_gallery
cd atv_gallery
```

### 2. تثبيت المكتبات
```bash
composer install
```

### 3. إعداد البيئة
```bash
cp .env.example .env
```

قم بتعديل ملف `.env` وضبط إعدادات قاعدة البيانات:
```env
DB_HOST=localhost
DB_PORT=3306
DB_DATABASE=atv_gallery
DB_USERNAME=root
DB_PASSWORD=your_password
```

### 4. إنشاء قاعدة البيانات
قم بإنشاء قاعدة بيانات جديدة:
```sql
CREATE DATABASE atv_gallery CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 5. تشغيل المايجريشن
```bash
php migrate.php
```

### 6. إدخال البيانات الأولية
```bash
php seed.php
```

### 7. ضبط الصلاحيات
```bash
chmod -R 755 public/uploads/
chmod -R 755 storage/
```

### 8. إعداد الخادم

#### Apache
تأكد من تفعيل mod_rewrite وإضافة ملف `.htaccess` في مجلد `public`:
```apache
RewriteEngine On
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.php [QSA,L]
```

#### Nginx
```nginx
location / {
    try_files $uri $uri/ /index.php?$query_string;
}
```

## الاستخدام

### تسجيل الدخول للإدارة
- الرابط: `http://your-domain/login`
- البريد الإلكتروني: `<EMAIL>`
- كلمة المرور: `admin123`

### رفع الصور
1. سجل دخولك للوحة الإدارة
2. اذهب إلى "رفع صور"
3. اسحب الصور أو انقر لاختيارها
4. اختر الأصناف المناسبة
5. انقر "رفع الصور"

### إدارة الأصناف
1. اذهب إلى "الأصناف" في لوحة الإدارة
2. انقر "إضافة صنف جديد"
3. أدخل الاسم والوصف
4. سيتم إنشاء الرابط تلقائياً

## البنية

```
atv_gallery/
├── public/                 # الملفات العامة
│   ├── index.php          # نقطة الدخول
│   ├── assets/            # CSS, JS, Images
│   └── uploads/           # الصور المرفوعة
├── src/                   # الكود المصدري
│   ├── Controllers/       # المتحكمات
│   ├── Models/           # نماذج البيانات
│   ├── Middleware/       # الوسطاء
│   ├── Services/         # الخدمات
│   └── config.php        # الإعدادات
├── views/                # القوالب
│   ├── layouts/          # التخطيطات الأساسية
│   ├── admin/            # صفحات الإدارة
│   └── public/           # الصفحات العامة
├── database/             # قاعدة البيانات
│   ├── migrations/       # المايجريشن
│   └── seeders/          # البيانات الأولية
└── storage/              # التخزين المؤقت
```

## الأمان

- تشفير كلمات المرور باستخدام `password_hash()`
- حماية من SQL Injection باستخدام Prepared Statements
- التحقق من صحة الملفات المرفوعة
- حماية مجلدات الرفع من التنفيذ المباشر
- جلسات آمنة مع HttpOnly cookies

## المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء branch جديد للميزة
3. Commit التغييرات
4. Push إلى البranch
5. إنشاء Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## الدعم

إذا واجهت أي مشاكل أو لديك اقتراحات، يرجى إنشاء Issue في GitHub.

## التحديثات المستقبلية

- [ ] دعم المزيد من أنواع الملفات
- [ ] نظام التعليقات
- [ ] تقييم الصور
- [ ] مشاركة على وسائل التواصل
- [ ] API للتطبيقات الخارجية
- [ ] نظام النسخ الاحتياطي التلقائي
