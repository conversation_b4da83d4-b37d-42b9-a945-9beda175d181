<?php
$content = ob_start();
?>

<!-- Hero Section -->
<?php if (empty($search) && empty($selectedCategory)): ?>
<section class="hero-section">
    <div class="container text-center">
        <h1 class="display-4 mb-4">
            <i class="fas fa-images me-3"></i>
            مرحباً بك في <?= APP_NAME ?>
        </h1>
        <p class="lead mb-4">
            استكشف مجموعة رائعة من الصور المصنفة والمنظمة بعناية
        </p>
        <div class="row justify-content-center">
            <div class="col-md-6">
                <form method="GET" action="/" class="d-flex">
                    <input type="text" 
                           name="search" 
                           class="form-control search-box me-2" 
                           placeholder="ابحث في الصور..."
                           value="<?= htmlspecialchars($search) ?>">
                    <button type="submit" class="btn btn-light btn-search">
                        <i class="fas fa-search"></i>
                    </button>
                </form>
            </div>
        </div>
    </div>
</section>
<?php endif; ?>

<div class="container">
    <!-- Search Results Header -->
    <?php if (!empty($search)): ?>
        <div class="alert alert-info">
            <i class="fas fa-search me-2"></i>
            نتائج البحث عن: "<strong><?= htmlspecialchars($search) ?></strong>"
            <?php if ($images['total'] > 0): ?>
                - تم العثور على <?= number_format($images['total']) ?> صورة
            <?php endif; ?>
            <a href="/" class="btn btn-sm btn-outline-primary ms-3">
                <i class="fas fa-times me-1"></i>مسح البحث
            </a>
        </div>
    <?php endif; ?>
    
    <!-- Category Header -->
    <?php if ($selectedCategory): ?>
        <div class="mb-4">
            <h2>
                <i class="fas fa-tag me-2"></i>
                <?= htmlspecialchars($selectedCategory['name']) ?>
            </h2>
            <?php if ($selectedCategory['description']): ?>
                <p class="text-muted"><?= htmlspecialchars($selectedCategory['description']) ?></p>
            <?php endif; ?>
            <div class="d-flex align-items-center">
                <span class="badge bg-primary me-2">
                    <?= number_format($images['total']) ?> صورة
                </span>
                <a href="/" class="btn btn-sm btn-outline-secondary">
                    <i class="fas fa-arrow-right me-1"></i>عرض جميع الصور
                </a>
            </div>
        </div>
    <?php endif; ?>
    
    <!-- Categories Filter -->
    <?php if (!empty($categories) && empty($selectedCategory)): ?>
        <div class="category-filter">
            <h5 class="mb-3">
                <i class="fas fa-filter me-2"></i>تصفية حسب الصنف
            </h5>
            <div class="text-center">
                <a href="/" class="category-btn <?= empty($selectedCategory) && empty($search) ? 'active' : '' ?>">
                    <i class="fas fa-th me-1"></i>جميع الصور
                </a>
                <?php foreach ($categories as $category): ?>
                    <?php if ($category['image_count'] > 0): ?>
                        <a href="/?category=<?= urlencode($category['slug']) ?>" 
                           class="category-btn">
                            <?= htmlspecialchars($category['name']) ?>
                            <span class="badge bg-light text-dark ms-1">
                                <?= number_format($category['image_count']) ?>
                            </span>
                        </a>
                    <?php endif; ?>
                <?php endforeach; ?>
            </div>
        </div>
    <?php endif; ?>
    
    <!-- Search Box (when not in hero) -->
    <?php if (!empty($search) || !empty($selectedCategory)): ?>
        <div class="row mb-4">
            <div class="col-md-6">
                <form method="GET" action="/" class="d-flex">
                    <?php if ($selectedCategory): ?>
                        <input type="hidden" name="category" value="<?= htmlspecialchars($selectedCategory['slug']) ?>">
                    <?php endif; ?>
                    <input type="text" 
                           name="search" 
                           class="form-control search-box me-2" 
                           placeholder="ابحث في الصور..."
                           value="<?= htmlspecialchars($search) ?>">
                    <button type="submit" class="btn btn-primary btn-search">
                        <i class="fas fa-search"></i>
                    </button>
                </form>
            </div>
        </div>
    <?php endif; ?>
    
    <!-- Images Grid -->
    <?php if (empty($images['data'])): ?>
        <div class="text-center py-5">
            <i class="fas fa-images fa-4x text-muted mb-4"></i>
            <h3>لا توجد صور</h3>
            <?php if (!empty($search)): ?>
                <p class="text-muted">لم يتم العثور على صور تطابق كلمة البحث</p>
                <a href="/" class="btn btn-primary">
                    <i class="fas fa-home me-2"></i>العودة للرئيسية
                </a>
            <?php elseif ($selectedCategory): ?>
                <p class="text-muted">لا توجد صور في هذا الصنف حالياً</p>
                <a href="/" class="btn btn-primary">
                    <i class="fas fa-th me-2"></i>عرض جميع الصور
                </a>
            <?php else: ?>
                <p class="text-muted">لم يتم رفع أي صور بعد</p>
            <?php endif; ?>
        </div>
    <?php else: ?>
        <div class="masonry-grid">
            <?php foreach ($images['data'] as $image): ?>
                <div class="masonry-item">
                    <div class="image-card">
                        <a href="/uploads/images/<?= htmlspecialchars($image['file_path']) ?>" 
                           data-lightbox="gallery" 
                           data-title="<?= htmlspecialchars($image['title']) ?>">
                            <img src="/uploads/thumbnails/<?= htmlspecialchars($image['thumb_path']) ?>" 
                                 class="card-img-top" 
                                 alt="<?= htmlspecialchars($image['alt_text']) ?>"
                                 style="width: 100%; height: auto; display: block;">
                        </a>
                        
                        <?php if ($image['title'] || $image['description']): ?>
                            <div class="card-body">
                                <?php if ($image['title']): ?>
                                    <h6 class="card-title mb-2">
                                        <?= htmlspecialchars($image['title']) ?>
                                    </h6>
                                <?php endif; ?>
                                
                                <?php if ($image['description']): ?>
                                    <p class="card-text small text-muted">
                                        <?= htmlspecialchars(mb_substr($image['description'], 0, 100)) ?>
                                        <?= mb_strlen($image['description']) > 100 ? '...' : '' ?>
                                    </p>
                                <?php endif; ?>
                                
                                <!-- Image metadata -->
                                <div class="d-flex justify-content-between align-items-center">
                                    <small class="text-muted">
                                        <?= date('Y/m/d', strtotime($image['created_at'])) ?>
                                    </small>
                                    
                                    <?php if (isset($image['meta']['width']) && isset($image['meta']['height'])): ?>
                                        <small class="text-muted">
                                            <?= $image['meta']['width'] ?>×<?= $image['meta']['height'] ?>
                                        </small>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
        
        <!-- Pagination -->
        <?php if ($images['last_page'] > 1): ?>
            <nav aria-label="صفحات المعرض" class="mt-5">
                <ul class="pagination justify-content-center">
                    <?php if ($images['current_page'] > 1): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?= $images['current_page'] - 1 ?><?= !empty($search) ? '&search=' . urlencode($search) : '' ?><?= $selectedCategory ? '&category=' . urlencode($selectedCategory['slug']) : '' ?>">
                                <i class="fas fa-chevron-right me-1"></i>السابق
                            </a>
                        </li>
                    <?php endif; ?>
                    
                    <?php for ($i = max(1, $images['current_page'] - 2); $i <= min($images['last_page'], $images['current_page'] + 2); $i++): ?>
                        <li class="page-item <?= $i == $images['current_page'] ? 'active' : '' ?>">
                            <a class="page-link" href="?page=<?= $i ?><?= !empty($search) ? '&search=' . urlencode($search) : '' ?><?= $selectedCategory ? '&category=' . urlencode($selectedCategory['slug']) : '' ?>">
                                <?= $i ?>
                            </a>
                        </li>
                    <?php endfor; ?>
                    
                    <?php if ($images['current_page'] < $images['last_page']): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?= $images['current_page'] + 1 ?><?= !empty($search) ? '&search=' . urlencode($search) : '' ?><?= $selectedCategory ? '&category=' . urlencode($selectedCategory['slug']) : '' ?>">
                                التالي<i class="fas fa-chevron-left ms-1"></i>
                            </a>
                        </li>
                    <?php endif; ?>
                </ul>
            </nav>
            
            <div class="text-center text-muted mt-3">
                عرض <?= number_format(($images['current_page'] - 1) * $images['per_page'] + 1) ?> - 
                <?= number_format(min($images['current_page'] * $images['per_page'], $images['total'])) ?> 
                من <?= number_format($images['total']) ?> صورة
            </div>
        <?php endif; ?>
    <?php endif; ?>
</div>

<?php
$content = ob_get_clean();

// Set page metadata
$title = APP_NAME;
if ($selectedCategory) {
    $title = $selectedCategory['name'] . ' - ' . APP_NAME;
    $description = $selectedCategory['description'] ?: 'صور من صنف ' . $selectedCategory['name'];
} elseif (!empty($search)) {
    $title = 'البحث: ' . $search . ' - ' . APP_NAME;
    $description = 'نتائج البحث عن: ' . $search;
} else {
    $description = 'معرض صور ATV - مجموعة رائعة من الصور المصنفة والمنظمة';
}

include VIEWS_PATH . 'layouts/public.php';
?>
