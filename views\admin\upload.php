<?php
$content = ob_start();
?>

<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-cloud-upload-alt me-2"></i>رفع صور جديدة
        </h5>
    </div>
    <div class="card-body">
        <form method="POST" action="/admin/upload" enctype="multipart/form-data" id="uploadForm">
            <div class="row">
                <div class="col-md-8">
                    <div class="mb-4">
                        <label for="images" class="form-label">اختر الصور</label>
                        <div class="upload-area" id="uploadArea">
                            <div class="upload-content text-center py-5">
                                <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                                <h5>اسحب الصور هنا أو انقر للاختيار</h5>
                                <p class="text-muted">يمكنك رفع عدة صور في نفس الوقت</p>
                                <p class="small text-muted">
                                    الأنواع المدعومة: JPEG, PNG, GIF, WebP<br>
                                    الحد الأقصى: <?= UPLOAD_MAX_SIZE / 1024 / 1024 ?>MB لكل صورة
                                </p>
                            </div>
                            <input type="file" 
                                   id="images" 
                                   name="images[]" 
                                   multiple 
                                   accept="image/*" 
                                   class="form-control d-none">
                        </div>
                        
                        <div id="previewContainer" class="mt-3" style="display: none;">
                            <h6>معاينة الصور المختارة:</h6>
                            <div id="imagePreview" class="row"></div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="mb-3">
                        <label class="form-label">ربط بالأصناف (اختياري)</label>
                        <div class="border rounded p-3" style="max-height: 300px; overflow-y: auto;">
                            <?php if (empty($categories)): ?>
                                <p class="text-muted small">لا توجد أصناف متاحة</p>
                                <a href="/admin/categories?action=create" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-plus me-1"></i>إضافة صنف
                                </a>
                            <?php else: ?>
                                <?php foreach ($categories as $category): ?>
                                    <div class="form-check mb-2">
                                        <input class="form-check-input" 
                                               type="checkbox" 
                                               name="categories[]" 
                                               value="<?= $category['id'] ?>" 
                                               id="category_<?= $category['id'] ?>">
                                        <label class="form-check-label" for="category_<?= $category['id'] ?>">
                                            <?= htmlspecialchars($category['name']) ?>
                                        </label>
                                    </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary" id="uploadBtn" disabled>
                            <i class="fas fa-upload me-2"></i>رفع الصور
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<?php
$additionalCSS = '
<style>
.upload-area {
    border: 2px dashed #dee2e6;
    border-radius: 10px;
    transition: all 0.3s ease;
    cursor: pointer;
}
.upload-area:hover,
.upload-area.dragover {
    border-color: #667eea;
    background-color: #f8f9ff;
}
.preview-item {
    position: relative;
    margin-bottom: 15px;
}
.preview-item img {
    width: 100%;
    height: 120px;
    object-fit: cover;
    border-radius: 8px;
}
.preview-item .remove-btn {
    position: absolute;
    top: 5px;
    right: 5px;
    background: rgba(220, 53, 69, 0.8);
    color: white;
    border: none;
    border-radius: 50%;
    width: 25px;
    height: 25px;
    font-size: 12px;
    cursor: pointer;
}
.preview-item .remove-btn:hover {
    background: rgba(220, 53, 69, 1);
}
</style>
';

$additionalJS = '
<script>
document.addEventListener("DOMContentLoaded", function() {
    const uploadArea = document.getElementById("uploadArea");
    const fileInput = document.getElementById("images");
    const previewContainer = document.getElementById("previewContainer");
    const imagePreview = document.getElementById("imagePreview");
    const uploadBtn = document.getElementById("uploadBtn");
    let selectedFiles = [];
    
    // Click to select files
    uploadArea.addEventListener("click", () => fileInput.click());
    
    // Drag and drop
    uploadArea.addEventListener("dragover", (e) => {
        e.preventDefault();
        uploadArea.classList.add("dragover");
    });
    
    uploadArea.addEventListener("dragleave", () => {
        uploadArea.classList.remove("dragover");
    });
    
    uploadArea.addEventListener("drop", (e) => {
        e.preventDefault();
        uploadArea.classList.remove("dragover");
        handleFiles(e.dataTransfer.files);
    });
    
    // File input change
    fileInput.addEventListener("change", (e) => {
        handleFiles(e.target.files);
    });
    
    function handleFiles(files) {
        selectedFiles = Array.from(files);
        updatePreview();
        updateUploadButton();
    }
    
    function updatePreview() {
        imagePreview.innerHTML = "";
        
        if (selectedFiles.length === 0) {
            previewContainer.style.display = "none";
            return;
        }
        
        previewContainer.style.display = "block";
        
        selectedFiles.forEach((file, index) => {
            if (file.type.startsWith("image/")) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    const col = document.createElement("div");
                    col.className = "col-md-3 col-sm-4 col-6";
                    col.innerHTML = `
                        <div class="preview-item">
                            <img src="${e.target.result}" alt="Preview">
                            <button type="button" class="remove-btn" onclick="removeFile(${index})">
                                <i class="fas fa-times"></i>
                            </button>
                            <div class="small text-muted mt-1 text-center">
                                ${file.name}
                            </div>
                        </div>
                    `;
                    imagePreview.appendChild(col);
                };
                reader.readAsDataURL(file);
            }
        });
    }
    
    function updateUploadButton() {
        uploadBtn.disabled = selectedFiles.length === 0;
    }
    
    window.removeFile = function(index) {
        selectedFiles.splice(index, 1);
        updateFileInput();
        updatePreview();
        updateUploadButton();
    };
    
    function updateFileInput() {
        const dt = new DataTransfer();
        selectedFiles.forEach(file => dt.items.add(file));
        fileInput.files = dt.files;
    }
});
</script>
';

$content = ob_get_clean();

// Include admin layout
$currentPage = 'upload';
$pageTitle = 'رفع صور جديدة';
$breadcrumb = [
    ['title' => 'الرئيسية', 'url' => '/admin'],
    ['title' => 'رفع صور']
];

include VIEWS_PATH . 'layouts/admin.php';
?>
