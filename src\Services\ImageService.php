<?php

namespace App\Services;

use Intervention\Image\ImageManager;
use Intervention\Image\Drivers\Gd\Driver;

class ImageService {
    private $manager;
    
    public function __construct() {
        $this->manager = new ImageManager(new Driver());
    }
    
    public function processUpload($file) {
        // Validate file
        $validation = $this->validateFile($file);
        if (!$validation['valid']) {
            return ['success' => false, 'error' => $validation['error']];
        }
        
        try {
            // Generate unique filename
            $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
            $filename = uniqid() . '_' . time() . '.' . $extension;
            $thumbFilename = 'thumb_' . $filename;
            
            // Create paths
            $originalPath = UPLOAD_PATH . $filename;
            $thumbPath = THUMBNAIL_PATH . $thumbFilename;
            
            // Ensure directories exist
            if (!is_dir(UPLOAD_PATH)) {
                mkdir(UPLOAD_PATH, 0755, true);
            }
            if (!is_dir(THUMBNAIL_PATH)) {
                mkdir(THUMBNAIL_PATH, 0755, true);
            }
            
            // Move original file
            if (!move_uploaded_file($file['tmp_name'], $originalPath)) {
                return ['success' => false, 'error' => 'فشل في رفع الملف'];
            }
            
            // Create thumbnail
            $image = $this->manager->read($originalPath);
            $originalWidth = $image->width();
            $originalHeight = $image->height();
            
            // Resize for thumbnail while maintaining aspect ratio
            $image->scale(width: THUMBNAIL_WIDTH);
            $image->save($thumbPath, quality: 85);
            
            // Extract EXIF data
            $exifData = $this->extractExifData($originalPath);
            
            // Prepare metadata
            $meta = [
                'original_name' => $file['name'],
                'size' => $file['size'],
                'mime' => $file['type'],
                'width' => $originalWidth,
                'height' => $originalHeight,
                'exif' => $exifData
            ];
            
            return [
                'success' => true,
                'filename' => $filename,
                'thumb_filename' => $thumbFilename,
                'meta' => $meta
            ];
            
        } catch (\Exception $e) {
            return ['success' => false, 'error' => 'خطأ في معالجة الصورة: ' . $e->getMessage()];
        }
    }
    
    private function validateFile($file) {
        // Check for upload errors
        if ($file['error'] !== UPLOAD_ERR_OK) {
            return ['valid' => false, 'error' => 'خطأ في رفع الملف'];
        }
        
        // Check file size
        if ($file['size'] > UPLOAD_MAX_SIZE) {
            $maxSizeMB = UPLOAD_MAX_SIZE / 1024 / 1024;
            return ['valid' => false, 'error' => "حجم الملف كبير جداً. الحد الأقصى {$maxSizeMB} ميجابايت"];
        }
        
        // Check file type
        $allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
        if (!in_array($file['type'], $allowedTypes)) {
            return ['valid' => false, 'error' => 'نوع الملف غير مدعوم. الأنواع المدعومة: JPEG, PNG, GIF, WebP'];
        }
        
        // Check if it's actually an image
        $imageInfo = getimagesize($file['tmp_name']);
        if ($imageInfo === false) {
            return ['valid' => false, 'error' => 'الملف ليس صورة صالحة'];
        }
        
        return ['valid' => true];
    }
    
    private function extractExifData($imagePath) {
        $exifData = [];
        
        try {
            if (function_exists('exif_read_data')) {
                $exif = exif_read_data($imagePath);
                if ($exif !== false) {
                    // Extract useful EXIF data
                    $exifData = [
                        'camera' => $exif['Model'] ?? null,
                        'make' => $exif['Make'] ?? null,
                        'date_taken' => $exif['DateTime'] ?? null,
                        'exposure_time' => $exif['ExposureTime'] ?? null,
                        'f_number' => $exif['FNumber'] ?? null,
                        'iso' => $exif['ISOSpeedRatings'] ?? null,
                        'focal_length' => $exif['FocalLength'] ?? null,
                    ];
                    
                    // Clean up null values
                    $exifData = array_filter($exifData, function($value) {
                        return $value !== null;
                    });
                }
            }
        } catch (\Exception $e) {
            // EXIF extraction failed, but that's okay
        }
        
        return $exifData;
    }
    
    public function deleteImage($filename, $thumbFilename) {
        $deleted = true;
        
        $originalPath = UPLOAD_PATH . $filename;
        $thumbPath = THUMBNAIL_PATH . $thumbFilename;
        
        if (file_exists($originalPath)) {
            $deleted = $deleted && unlink($originalPath);
        }
        
        if (file_exists($thumbPath)) {
            $deleted = $deleted && unlink($thumbPath);
        }
        
        return $deleted;
    }
}
