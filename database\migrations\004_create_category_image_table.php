<?php

class CreateCategoryImageTable {
    
    public static function up($pdo) {
        $sql = "
            CREATE TABLE IF NOT EXISTS category_image (
                category_id INT NOT NULL,
                image_id INT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                <PERSON><PERSON>AR<PERSON> KEY (category_id, image_id),
                FOREIG<PERSON> KEY (category_id) REFERENCES categories(id) ON DELETE CASCADE,
                FOREIGN KEY (image_id) REFERENCES images(id) ON DELETE CASCADE,
                INDEX idx_category_id (category_id),
                INDEX idx_image_id (image_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        
        $pdo->exec($sql);
        echo "Category_image pivot table created successfully.\n";
    }
    
    public static function down($pdo) {
        $sql = "DROP TABLE IF EXISTS category_image";
        $pdo->exec($sql);
        echo "Category_image pivot table dropped successfully.\n";
    }
}
