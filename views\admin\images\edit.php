<?php
$content = ob_start();
?>

<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-edit me-2"></i>تعديل الصورة: <?= htmlspecialchars($image['title']) ?>
        </h5>
    </div>
    <div class="card-body">
        <form method="POST" action="/admin/images?action=edit&id=<?= $image['id'] ?>">
            <div class="row">
                <div class="col-md-8">
                    <div class="mb-3">
                        <label for="title" class="form-label">عنوان الصورة <span class="text-danger">*</span></label>
                        <input type="text" 
                               class="form-control" 
                               id="title" 
                               name="title" 
                               value="<?= htmlspecialchars($image['title']) ?>"
                               required
                               placeholder="عنوان وصفي للصورة">
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">الوصف (اختياري)</label>
                        <textarea class="form-control" 
                                  id="description" 
                                  name="description" 
                                  rows="4"
                                  placeholder="وصف تفصيلي للصورة..."><?= htmlspecialchars($image['description'] ?? '') ?></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="alt_text" class="form-label">النص البديل (Alt Text)</label>
                        <input type="text" 
                               class="form-control" 
                               id="alt_text" 
                               name="alt_text" 
                               value="<?= htmlspecialchars($image['alt_text'] ?? '') ?>"
                               placeholder="وصف مختصر للصورة لمحركات البحث">
                        <div class="form-text">يساعد في تحسين SEO وإمكانية الوصول</div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">الأصناف</label>
                        <div class="border rounded p-3" style="max-height: 200px; overflow-y: auto;">
                            <?php if (empty($categories)): ?>
                                <p class="text-muted small">لا توجد أصناف متاحة</p>
                                <a href="/admin/categories?action=create" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-plus me-1"></i>إضافة صنف
                                </a>
                            <?php else: ?>
                                <?php foreach ($categories as $category): ?>
                                    <div class="form-check mb-2">
                                        <input class="form-check-input" 
                                               type="checkbox" 
                                               name="categories[]" 
                                               value="<?= $category['id'] ?>" 
                                               id="category_<?= $category['id'] ?>"
                                               <?= in_array($category['id'], $imageCategoryIds) ? 'checked' : '' ?>>
                                        <label class="form-check-label" for="category_<?= $category['id'] ?>">
                                            <?= htmlspecialchars($category['name']) ?>
                                        </label>
                                    </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <!-- Image Preview -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0">معاينة الصورة</h6>
                        </div>
                        <div class="card-body p-0">
                            <img src="/uploads/thumbnails/<?= htmlspecialchars($image['thumb_path']) ?>" 
                                 class="card-img-top" 
                                 alt="<?= htmlspecialchars($image['alt_text']) ?>"
                                 style="width: 100%; height: auto;">
                        </div>
                        <div class="card-body">
                            <div class="d-grid">
                                <a href="/uploads/images/<?= htmlspecialchars($image['file_path']) ?>" 
                                   class="btn btn-outline-primary btn-sm" 
                                   target="_blank">
                                    <i class="fas fa-external-link-alt me-1"></i>عرض بالحجم الكامل
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Image Info -->
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">معلومات الصورة</h6>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled small mb-0">
                                <li><strong>تاريخ الرفع:</strong><br>
                                    <?= date('Y/m/d H:i', strtotime($image['created_at'])) ?>
                                </li>
                                <li><strong>آخر تحديث:</strong><br>
                                    <?= date('Y/m/d H:i', strtotime($image['updated_at'])) ?>
                                </li>
                                
                                <?php if (isset($image['meta']['original_name'])): ?>
                                    <li><strong>الاسم الأصلي:</strong><br>
                                        <?= htmlspecialchars($image['meta']['original_name']) ?>
                                    </li>
                                <?php endif; ?>
                                
                                <?php if (isset($image['meta']['size'])): ?>
                                    <li><strong>حجم الملف:</strong><br>
                                        <?= number_format($image['meta']['size'] / 1024, 1) ?> KB
                                    </li>
                                <?php endif; ?>
                                
                                <?php if (isset($image['meta']['width']) && isset($image['meta']['height'])): ?>
                                    <li><strong>الأبعاد:</strong><br>
                                        <?= $image['meta']['width'] ?>×<?= $image['meta']['height'] ?> بكسل
                                    </li>
                                <?php endif; ?>
                                
                                <?php if (isset($image['meta']['mime'])): ?>
                                    <li><strong>نوع الملف:</strong><br>
                                        <?= htmlspecialchars($image['meta']['mime']) ?>
                                    </li>
                                <?php endif; ?>
                            </ul>
                            
                            <!-- EXIF Data -->
                            <?php if (isset($image['meta']['exif']) && !empty($image['meta']['exif'])): ?>
                                <hr>
                                <h6 class="small">بيانات EXIF</h6>
                                <ul class="list-unstyled small mb-0">
                                    <?php foreach ($image['meta']['exif'] as $key => $value): ?>
                                        <li><strong><?= htmlspecialchars($key) ?>:</strong><br>
                                            <?= htmlspecialchars($value) ?>
                                        </li>
                                    <?php endforeach; ?>
                                </ul>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="d-flex justify-content-between">
                <a href="/admin/images" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-2"></i>إلغاء
                </a>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save me-2"></i>حفظ التغييرات
                </button>
            </div>
        </form>
    </div>
</div>

<?php
$content = ob_get_clean();

// Include admin layout
$currentPage = 'images';
$pageTitle = 'تعديل الصورة';
$breadcrumb = [
    ['title' => 'الرئيسية', 'url' => '/admin'],
    ['title' => 'الصور', 'url' => '/admin/images'],
    ['title' => 'تعديل: ' . $image['title']]
];

include VIEWS_PATH . 'layouts/admin.php';
?>
