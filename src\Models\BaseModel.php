<?php

namespace App\Models;

use PDO;

abstract class BaseModel {
    protected $pdo;
    protected $table;
    protected $primaryKey = 'id';
    protected $fillable = [];
    
    public function __construct() {
        $this->pdo = \Database::getConnection();
    }
    
    public function find($id) {
        $stmt = $this->pdo->prepare("SELECT * FROM {$this->table} WHERE {$this->primaryKey} = ?");
        $stmt->execute([$id]);
        return $stmt->fetch();
    }
    
    public function findBy($column, $value) {
        $stmt = $this->pdo->prepare("SELECT * FROM {$this->table} WHERE {$column} = ?");
        $stmt->execute([$value]);
        return $stmt->fetch();
    }
    
    public function all($orderBy = null, $direction = 'ASC') {
        $sql = "SELECT * FROM {$this->table}";
        if ($orderBy) {
            $sql .= " ORDER BY {$orderBy} {$direction}";
        }
        $stmt = $this->pdo->query($sql);
        return $stmt->fetchAll();
    }
    
    public function paginate($page = 1, $perPage = 10, $orderBy = null, $direction = 'ASC') {
        $offset = ($page - 1) * $perPage;
        
        $sql = "SELECT * FROM {$this->table}";
        if ($orderBy) {
            $sql .= " ORDER BY {$orderBy} {$direction}";
        }
        $sql .= " LIMIT {$perPage} OFFSET {$offset}";
        
        $stmt = $this->pdo->query($sql);
        $data = $stmt->fetchAll();
        
        // Get total count
        $countStmt = $this->pdo->query("SELECT COUNT(*) FROM {$this->table}");
        $total = $countStmt->fetchColumn();
        
        return [
            'data' => $data,
            'total' => $total,
            'per_page' => $perPage,
            'current_page' => $page,
            'last_page' => ceil($total / $perPage)
        ];
    }
    
    public function create($data) {
        $data = $this->filterFillable($data);
        $columns = implode(',', array_keys($data));
        $placeholders = ':' . implode(', :', array_keys($data));
        
        $sql = "INSERT INTO {$this->table} ({$columns}) VALUES ({$placeholders})";
        $stmt = $this->pdo->prepare($sql);
        
        if ($stmt->execute($data)) {
            return $this->find($this->pdo->lastInsertId());
        }
        return false;
    }
    
    public function update($id, $data) {
        $data = $this->filterFillable($data);
        $setParts = [];
        foreach (array_keys($data) as $key) {
            $setParts[] = "{$key} = :{$key}";
        }
        $setClause = implode(', ', $setParts);
        
        $sql = "UPDATE {$this->table} SET {$setClause} WHERE {$this->primaryKey} = :id";
        $data['id'] = $id;
        
        $stmt = $this->pdo->prepare($sql);
        if ($stmt->execute($data)) {
            return $this->find($id);
        }
        return false;
    }
    
    public function delete($id) {
        $stmt = $this->pdo->prepare("DELETE FROM {$this->table} WHERE {$this->primaryKey} = ?");
        return $stmt->execute([$id]);
    }
    
    public function search($column, $term, $orderBy = null, $direction = 'ASC') {
        $sql = "SELECT * FROM {$this->table} WHERE {$column} LIKE ?";
        if ($orderBy) {
            $sql .= " ORDER BY {$orderBy} {$direction}";
        }
        
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute(["%{$term}%"]);
        return $stmt->fetchAll();
    }
    
    protected function filterFillable($data) {
        if (empty($this->fillable)) {
            return $data;
        }
        
        return array_intersect_key($data, array_flip($this->fillable));
    }
}
