<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $title ?? 'لوحة الإدارة' ?> - <?= APP_NAME ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="/assets/css/rtl-custom.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .sidebar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
        }
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 12px 20px;
            margin: 5px 15px;
            border-radius: 10px;
            transition: all 0.3s ease;
        }
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background: rgba(255,255,255,0.2);
            transform: translateX(-5px);
        }
        .main-content {
            padding: 20px;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
        }
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }
        .btn {
            border-radius: 10px;
            padding: 8px 20px;
            font-weight: 500;
        }
        .alert {
            border-radius: 10px;
            border: none;
        }
        .navbar-brand {
            font-weight: 700;
            color: white !important;
        }
        .table {
            border-radius: 10px;
            overflow: hidden;
        }
        .table th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            font-weight: 600;
        }
        .badge {
            border-radius: 20px;
            padding: 5px 12px;
        }
    </style>
    <?= $additionalCSS ?? '' ?>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 px-0">
                <div class="sidebar">
                    <div class="p-3 text-center border-bottom border-light border-opacity-25">
                        <h4 class="text-white mb-0">
                            <i class="fas fa-images me-2"></i>
                            <?= APP_NAME ?>
                        </h4>
                        <small class="text-white-50">لوحة الإدارة</small>
                    </div>
                    
                    <nav class="nav flex-column py-3">
                        <a class="nav-link <?= $currentPage === 'dashboard' ? 'active' : '' ?>" href="/admin">
                            <i class="fas fa-tachometer-alt me-2"></i>
                            الرئيسية
                        </a>
                        <a class="nav-link <?= $currentPage === 'categories' ? 'active' : '' ?>" href="/admin/categories">
                            <i class="fas fa-tags me-2"></i>
                            الأصناف
                        </a>
                        <a class="nav-link <?= $currentPage === 'images' ? 'active' : '' ?>" href="/admin/images">
                            <i class="fas fa-images me-2"></i>
                            الصور
                        </a>
                        <a class="nav-link <?= $currentPage === 'upload' ? 'active' : '' ?>" href="/admin/upload">
                            <i class="fas fa-cloud-upload-alt me-2"></i>
                            رفع صور
                        </a>
                        <div class="dropdown-divider mx-3 my-2 border-light border-opacity-25"></div>
                        <a class="nav-link" href="/" target="_blank">
                            <i class="fas fa-external-link-alt me-2"></i>
                            عرض المعرض
                        </a>
                        <a class="nav-link" href="/logout">
                            <i class="fas fa-sign-out-alt me-2"></i>
                            تسجيل الخروج
                        </a>
                    </nav>
                </div>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10">
                <div class="main-content">
                    <!-- Header -->
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <div>
                            <h2 class="mb-1"><?= $pageTitle ?? 'لوحة الإدارة' ?></h2>
                            <?php if (isset($breadcrumb)): ?>
                                <nav aria-label="breadcrumb">
                                    <ol class="breadcrumb">
                                        <?php foreach ($breadcrumb as $item): ?>
                                            <?php if (isset($item['url'])): ?>
                                                <li class="breadcrumb-item">
                                                    <a href="<?= $item['url'] ?>"><?= $item['title'] ?></a>
                                                </li>
                                            <?php else: ?>
                                                <li class="breadcrumb-item active"><?= $item['title'] ?></li>
                                            <?php endif; ?>
                                        <?php endforeach; ?>
                                    </ol>
                                </nav>
                            <?php endif; ?>
                        </div>
                        
                        <div class="text-muted">
                            <i class="fas fa-user me-2"></i>
                            مرحباً، <?= $_SESSION['user_name'] ?? 'المشرف' ?>
                        </div>
                    </div>
                    
                    <!-- Alerts -->
                    <?php if (isset($_SESSION['success'])): ?>
                        <div class="alert alert-success alert-dismissible fade show">
                            <i class="fas fa-check-circle me-2"></i>
                            <?= $_SESSION['success'] ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                        <?php unset($_SESSION['success']); ?>
                    <?php endif; ?>
                    
                    <?php if (isset($_SESSION['error'])): ?>
                        <div class="alert alert-danger alert-dismissible fade show">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <?= $_SESSION['error'] ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                        <?php unset($_SESSION['error']); ?>
                    <?php endif; ?>
                    
                    <!-- Page Content -->
                    <?= $content ?>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <?= $additionalJS ?? '' ?>
</body>
</html>
