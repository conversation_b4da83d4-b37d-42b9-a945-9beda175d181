<?php
/**
 * Simple Bootstrap file for ATV Gallery
 * Use this if Composer is not available
 */

// Simple autoloader
spl_autoload_register(function ($class) {
    // Convert namespace to file path
    $file = str_replace(['App\\', '\\'], ['src/', '/'], $class) . '.php';
    
    if (file_exists($file)) {
        require_once $file;
    }
});

// Load Dotenv manually (simple version)
class SimpleDotenv {
    public static function load($path) {
        if (!file_exists($path . '/.env')) {
            return;
        }
        
        $lines = file($path . '/.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        
        foreach ($lines as $line) {
            if (strpos($line, '#') === 0) {
                continue; // Skip comments
            }
            
            if (strpos($line, '=') !== false) {
                list($key, $value) = explode('=', $line, 2);
                $key = trim($key);
                $value = trim($value);
                
                // Remove quotes if present
                if (preg_match('/^"(.*)"$/', $value, $matches)) {
                    $value = $matches[1];
                } elseif (preg_match("/^'(.*)'$/", $value, $matches)) {
                    $value = $matches[1];
                }
                
                $_ENV[$key] = $value;
                putenv("$key=$value");
            }
        }
    }
}

// Create a simple Dotenv class for compatibility
class Dotenv {
    public static function createImmutable($path) {
        return new class($path) {
            private $path;
            
            public function __construct($path) {
                $this->path = $path;
            }
            
            public function load() {
                SimpleDotenv::load($this->path);
            }
        };
    }
}
