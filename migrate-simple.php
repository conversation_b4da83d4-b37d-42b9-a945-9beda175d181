<?php

// Use simple bootstrap if Composer is not available
if (file_exists(__DIR__ . '/vendor/autoload.php')) {
    require_once __DIR__ . '/vendor/autoload.php';
    require_once __DIR__ . '/src/config.php';
} else {
    echo "Composer not found, using simple bootstrap...\n";
    require_once __DIR__ . '/bootstrap.php';
    
    // Load environment variables
    $dotenv = Dotenv::createImmutable(__DIR__);
    $dotenv->load();
    
    // Database configuration
    define('DB_HOST', $_ENV['DB_HOST'] ?? 'localhost');
    define('DB_PORT', $_ENV['DB_PORT'] ?? '3306');
    define('DB_DATABASE', $_ENV['DB_DATABASE'] ?? 'atv_gallery');
    define('DB_USERNAME', $_ENV['DB_USERNAME'] ?? 'root');
    define('DB_PASSWORD', $_ENV['DB_PASSWORD'] ?? '');
    
    // Database connection class
    class Database {
        private static $connection = null;
        
        public static function getConnection() {
            if (self::$connection === null) {
                try {
                    $dsn = "mysql:host=" . DB_HOST . ";port=" . DB_PORT . ";dbname=" . DB_DATABASE . ";charset=utf8mb4";
                    self::$connection = new PDO($dsn, DB_USERNAME, DB_PASSWORD, [
                        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                        PDO::ATTR_EMULATE_PREPARES => false,
                    ]);
                } catch (PDOException $e) {
                    die("Database connection failed: " . $e->getMessage() . "\n");
                }
            }
            return self::$connection;
        }
    }
}

class SimpleMigrator {
    private $pdo;
    private $migrationsPath;
    
    public function __construct() {
        $this->pdo = Database::getConnection();
        $this->migrationsPath = __DIR__ . '/database/migrations/';
        $this->createMigrationsTable();
    }
    
    private function createMigrationsTable() {
        $sql = "
            CREATE TABLE IF NOT EXISTS migrations (
                id INT AUTO_INCREMENT PRIMARY KEY,
                migration VARCHAR(255) NOT NULL,
                executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        $this->pdo->exec($sql);
    }
    
    public function migrate() {
        $files = glob($this->migrationsPath . '*.php');
        sort($files);
        
        foreach ($files as $file) {
            $migrationName = basename($file, '.php');
            
            // Check if migration already executed
            $stmt = $this->pdo->prepare("SELECT COUNT(*) FROM migrations WHERE migration = ?");
            $stmt->execute([$migrationName]);
            
            if ($stmt->fetchColumn() == 0) {
                echo "Running migration: $migrationName\n";
                
                require_once $file;
                $className = $this->getClassNameFromFile($migrationName);
                
                if (class_exists($className)) {
                    $className::up($this->pdo);
                    
                    // Record migration
                    $stmt = $this->pdo->prepare("INSERT INTO migrations (migration) VALUES (?)");
                    $stmt->execute([$migrationName]);
                    
                    echo "Migration $migrationName completed.\n";
                } else {
                    echo "Class $className not found in $migrationName\n";
                }
            } else {
                echo "Migration $migrationName already executed.\n";
            }
        }
        
        echo "All migrations completed.\n";
    }
    
    private function getClassNameFromFile($filename) {
        // Convert filename like "001_create_users_table" to "CreateUsersTable"
        $parts = explode('_', $filename);
        array_shift($parts); // Remove number prefix
        return implode('', array_map('ucfirst', $parts));
    }
}

// Command line interface
if (php_sapi_name() === 'cli') {
    try {
        $migrator = new SimpleMigrator();
        $migrator->migrate();
    } catch (Exception $e) {
        echo "Error: " . $e->getMessage() . "\n";
        exit(1);
    }
} else {
    echo "This script must be run from command line.\n";
}
