<?php

namespace App\Models;

class User extends BaseModel {
    protected $table = 'users';
    protected $fillable = ['email', 'password', 'name', 'is_admin'];
    
    public function create($data) {
        // Hash password before saving
        if (isset($data['password'])) {
            $data['password'] = password_hash($data['password'], PASSWORD_DEFAULT);
        }
        
        return parent::create($data);
    }
    
    public function update($id, $data) {
        // Hash password if provided
        if (isset($data['password']) && !empty($data['password'])) {
            $data['password'] = password_hash($data['password'], PASSWORD_DEFAULT);
        } else {
            // Remove password from update if empty
            unset($data['password']);
        }
        
        return parent::update($id, $data);
    }
    
    public function findByEmail($email) {
        return $this->findBy('email', $email);
    }
    
    public function verifyPassword($email, $password) {
        $user = $this->findByEmail($email);
        if ($user && password_verify($password, $user['password'])) {
            return $user;
        }
        return false;
    }
    
    public function isAdmin($userId) {
        $user = $this->find($userId);
        return $user && $user['is_admin'] == 1;
    }
}
