<?php

echo "Using MySQLi version for seeding...\n";

// Load environment variables
if (file_exists('.env')) {
    $envContent = file_get_contents('.env');
    $envLines = explode("\n", $envContent);
    
    foreach ($envLines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value);
        }
    }
}

// Database configuration
$dbHost = $_ENV['DB_HOST'] ?? 'localhost';
$dbPort = $_ENV['DB_PORT'] ?? '3306';
$dbName = $_ENV['DB_DATABASE'] ?? 'atv_gallery';
$dbUser = $_ENV['DB_USERNAME'] ?? 'root';
$dbPass = $_ENV['DB_PASSWORD'] ?? '';

echo "Connecting to database: $dbHost:$dbPort/$dbName\n";

// Connect to MySQL
$mysqli = new mysqli($dbHost, $dbUser, $dbPass, $dbName, $dbPort);

// Check connection
if ($mysqli->connect_error) {
    die("Connection failed: " . $mysqli->connect_error . "\n");
}

// Set charset
$mysqli->set_charset("utf8mb4");

echo "✓ Connected to database successfully\n\n";

class MySQLiSeeder {
    private $mysqli;
    
    public function __construct($mysqli) {
        $this->mysqli = $mysqli;
    }
    
    public function run() {
        echo "Starting database seeding...\n";
        
        $this->seedUsers();
        $this->seedCategories();
        
        echo "Database seeding completed successfully!\n";
    }
    
    private function seedUsers() {
        echo "Seeding users...\n";
        
        // Check if admin user already exists
        $stmt = $this->mysqli->prepare("SELECT COUNT(*) FROM users WHERE email = ?");
        $email = '<EMAIL>';
        $stmt->bind_param("s", $email);
        $stmt->execute();
        $result = $stmt->get_result();
        $count = $result->fetch_row()[0];
        $stmt->close();
        
        if ($count == 0) {
            // Create admin user
            $stmt = $this->mysqli->prepare("
                INSERT INTO users (email, password, name, is_admin, created_at, updated_at) 
                VALUES (?, ?, ?, ?, NOW(), NOW())
            ");
            
            $email = '<EMAIL>';
            $hashedPassword = password_hash('admin123', PASSWORD_DEFAULT);
            $name = 'مدير النظام';
            $isAdmin = 1;
            
            $stmt->bind_param("sssi", $email, $hashedPassword, $name, $isAdmin);
            
            if ($stmt->execute()) {
                echo "Admin user created: <EMAIL> / admin123\n";
            } else {
                echo "Error creating admin user: " . $stmt->error . "\n";
            }
            $stmt->close();
        } else {
            echo "Admin user already exists.\n";
        }
        
        // Create a regular user for testing
        $stmt = $this->mysqli->prepare("SELECT COUNT(*) FROM users WHERE email = ?");
        $email = '<EMAIL>';
        $stmt->bind_param("s", $email);
        $stmt->execute();
        $result = $stmt->get_result();
        $count = $result->fetch_row()[0];
        $stmt->close();
        
        if ($count == 0) {
            $stmt = $this->mysqli->prepare("
                INSERT INTO users (email, password, name, is_admin, created_at, updated_at) 
                VALUES (?, ?, ?, ?, NOW(), NOW())
            ");
            
            $email = '<EMAIL>';
            $hashedPassword = password_hash('user123', PASSWORD_DEFAULT);
            $name = 'مستخدم عادي';
            $isAdmin = 0;
            
            $stmt->bind_param("sssi", $email, $hashedPassword, $name, $isAdmin);
            
            if ($stmt->execute()) {
                echo "Regular user created: <EMAIL> / user123\n";
            } else {
                echo "Error creating regular user: " . $stmt->error . "\n";
            }
            $stmt->close();
        } else {
            echo "Regular user already exists.\n";
        }
    }
    
    private function seedCategories() {
        echo "Seeding categories...\n";
        
        $categories = [
            [
                'name' => 'طبيعة',
                'slug' => 'nature',
                'description' => 'صور طبيعية خلابة من مناظر طبيعية وحدائق ومناظر جبلية'
            ],
            [
                'name' => 'مدن',
                'slug' => 'cities',
                'description' => 'صور من المدن والمباني والعمارة الحديثة والتراثية'
            ],
            [
                'name' => 'أشخاص',
                'slug' => 'people',
                'description' => 'صور شخصية وجماعية ولقطات من الحياة اليومية'
            ],
            [
                'name' => 'طعام',
                'slug' => 'food',
                'description' => 'صور للأطعمة والمشروبات والوصفات'
            ],
            [
                'name' => 'تقنية',
                'slug' => 'technology',
                'description' => 'صور للأجهزة التقنية والابتكارات والتطبيقات'
            ],
            [
                'name' => 'رياضة',
                'slug' => 'sports',
                'description' => 'صور رياضية من مختلف الألعاب والأنشطة الرياضية'
            ],
            [
                'name' => 'فن',
                'slug' => 'art',
                'description' => 'أعمال فنية ولوحات ومنحوتات وإبداعات فنية'
            ],
            [
                'name' => 'سفر',
                'slug' => 'travel',
                'description' => 'صور من الرحلات والسفر والمعالم السياحية'
            ]
        ];
        
        foreach ($categories as $category) {
            // Check if category already exists
            $stmt = $this->mysqli->prepare("SELECT COUNT(*) FROM categories WHERE slug = ?");
            $stmt->bind_param("s", $category['slug']);
            $stmt->execute();
            $result = $stmt->get_result();
            $count = $result->fetch_row()[0];
            $stmt->close();
            
            if ($count == 0) {
                $stmt = $this->mysqli->prepare("
                    INSERT INTO categories (name, slug, description, created_at, updated_at) 
                    VALUES (?, ?, ?, NOW(), NOW())
                ");
                
                $stmt->bind_param("sss", $category['name'], $category['slug'], $category['description']);
                
                if ($stmt->execute()) {
                    echo "Category created: {$category['name']}\n";
                } else {
                    echo "Error creating category {$category['name']}: " . $stmt->error . "\n";
                }
                $stmt->close();
            } else {
                echo "Category already exists: {$category['name']}\n";
            }
        }
    }
}

// Run seeder
try {
    $seeder = new MySQLiSeeder($mysqli);
    $seeder->run();
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    exit(1);
} finally {
    $mysqli->close();
}
