<?php
$content = ob_start();
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h4 class="mb-0">إدارة الأصناف</h4>
    <a href="/admin/categories?action=create" class="btn btn-primary">
        <i class="fas fa-plus me-2"></i>إضافة صنف جديد
    </a>
</div>

<?php if (empty($categories)): ?>
    <div class="card">
        <div class="card-body text-center py-5">
            <i class="fas fa-tags fa-3x text-muted mb-3"></i>
            <h5>لا توجد أصناف</h5>
            <p class="text-muted">ابدأ بإضافة صنف جديد لتنظيم صورك</p>
            <a href="/admin/categories?action=create" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>إضافة صنف جديد
            </a>
        </div>
    </div>
<?php else: ?>
    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>الاسم</th>
                            <th>الرابط</th>
                            <th>الوصف</th>
                            <th>عدد الصور</th>
                            <th>تاريخ الإنشاء</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($categories as $category): ?>
                            <tr>
                                <td>
                                    <strong><?= htmlspecialchars($category['name']) ?></strong>
                                </td>
                                <td>
                                    <code><?= htmlspecialchars($category['slug']) ?></code>
                                </td>
                                <td>
                                    <?php if ($category['description']): ?>
                                        <?= htmlspecialchars(mb_substr($category['description'], 0, 50)) ?>
                                        <?= mb_strlen($category['description']) > 50 ? '...' : '' ?>
                                    <?php else: ?>
                                        <span class="text-muted">لا يوجد وصف</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="badge bg-primary">
                                        <?= number_format($category['image_count']) ?>
                                    </span>
                                </td>
                                <td>
                                    <small class="text-muted">
                                        <?= date('Y/m/d H:i', strtotime($category['created_at'])) ?>
                                    </small>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="/category/<?= $category['slug'] ?>" 
                                           class="btn btn-outline-info" 
                                           target="_blank"
                                           title="معاينة">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="/admin/categories?action=edit&id=<?= $category['id'] ?>" 
                                           class="btn btn-outline-primary"
                                           title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button" 
                                                class="btn btn-outline-danger"
                                                onclick="confirmDelete(<?= $category['id'] ?>, '<?= htmlspecialchars($category['name']) ?>')"
                                                title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
<?php endif; ?>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف الصنف "<span id="categoryName"></span>"؟</p>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>تحذير:</strong> سيتم إلغاء ربط جميع الصور بهذا الصنف، لكن الصور نفسها لن تُحذف.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <a href="#" id="deleteLink" class="btn btn-danger">
                    <i class="fas fa-trash me-2"></i>حذف
                </a>
            </div>
        </div>
    </div>
</div>

<?php
$additionalJS = '
<script>
function confirmDelete(id, name) {
    document.getElementById("categoryName").textContent = name;
    document.getElementById("deleteLink").href = "/admin/categories?action=delete&id=" + id;
    new bootstrap.Modal(document.getElementById("deleteModal")).show();
}
</script>
';

$content = ob_get_clean();

// Include admin layout
$currentPage = 'categories';
$pageTitle = 'إدارة الأصناف';
$breadcrumb = [
    ['title' => 'الرئيسية', 'url' => '/admin'],
    ['title' => 'الأصناف']
];

include VIEWS_PATH . 'layouts/admin.php';
?>
