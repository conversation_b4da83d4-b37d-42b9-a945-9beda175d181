<?php

namespace App\Controllers;

use App\Models\User;

class AuthController {
    private $userModel;
    
    public function __construct() {
        $this->userModel = new User();
    }
    
    public function showLogin() {
        // If already logged in, redirect to admin
        if (isLoggedIn()) {
            redirect('/admin');
        }
        
        $error = $_SESSION['error'] ?? null;
        unset($_SESSION['error']);
        
        view('auth/login', [
            'error' => $error,
            'redirect' => $_GET['redirect'] ?? '/admin'
        ]);
    }
    
    public function login() {
        $email = $_POST['email'] ?? '';
        $password = $_POST['password'] ?? '';
        $redirect = $_POST['redirect'] ?? '/admin';
        
        // Validate input
        if (empty($email) || empty($password)) {
            $_SESSION['error'] = 'البريد الإلكتروني وكلمة المرور مطلوبان';
            redirect('/login');
        }
        
        // Verify credentials
        $user = $this->userModel->verifyPassword($email, $password);
        
        if ($user) {
            // Set session
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['user_name'] = $user['name'];
            $_SESSION['user_email'] = $user['email'];
            $_SESSION['is_admin'] = $user['is_admin'];
            
            // Redirect to intended page
            redirect($redirect);
        } else {
            $_SESSION['error'] = 'البريد الإلكتروني أو كلمة المرور غير صحيحة';
            redirect('/login');
        }
    }
    
    public function logout() {
        // Destroy session
        session_destroy();
        redirect('/');
    }
}
