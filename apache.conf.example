# ATV Gallery - Apache Virtual Host Configuration Example
# Copy this to your Apache sites-available directory and modify as needed

<VirtualHost *:80>
    ServerName your-domain.com
    ServerAlias www.your-domain.com
    DocumentRoot /path/to/atv_gallery/public
    
    # Directory configuration
    <Directory /path/to/atv_gallery/public>
        Options -Indexes +FollowSymLinks
        AllowOverride All
        Require all granted
        
        # Enable URL Rewriting
        RewriteEngine On
        RewriteCond %{REQUEST_FILENAME} !-f
        RewriteCond %{REQUEST_FILENAME} !-d
        RewriteRule ^(.*)$ index.php [QSA,L]
    </Directory>
    
    # Prevent access to sensitive directories
    <Directory /path/to/atv_gallery/src>
        Require all denied
    </Directory>
    
    <Directory /path/to/atv_gallery/database>
        Require all denied
    </Directory>
    
    <Directory /path/to/atv_gallery/storage>
        Require all denied
    </Directory>
    
    <Directory /path/to/atv_gallery/vendor>
        Require all denied
    </Directory>
    
    # Prevent access to sensitive files
    <FilesMatch "\.(env|log|md|json|lock|php~|bak)$">
        Require all denied
    </FilesMatch>
    
    # Prevent PHP execution in uploads directory
    <Directory /path/to/atv_gallery/public/uploads>
        <FilesMatch "\.php$">
            Require all denied
        </FilesMatch>
    </Directory>
    
    # Security headers
    <IfModule mod_headers.c>
        Header always set X-Content-Type-Options nosniff
        Header always set X-XSS-Protection "1; mode=block"
        Header always set X-Frame-Options DENY
        Header always set Referrer-Policy "strict-origin-when-cross-origin"
        Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; img-src 'self' data: https:; font-src 'self' https://cdnjs.cloudflare.com;"
    </IfModule>
    
    # Compression
    <IfModule mod_deflate.c>
        AddOutputFilterByType DEFLATE text/plain
        AddOutputFilterByType DEFLATE text/html
        AddOutputFilterByType DEFLATE text/xml
        AddOutputFilterByType DEFLATE text/css
        AddOutputFilterByType DEFLATE application/xml
        AddOutputFilterByType DEFLATE application/xhtml+xml
        AddOutputFilterByType DEFLATE application/rss+xml
        AddOutputFilterByType DEFLATE application/javascript
        AddOutputFilterByType DEFLATE application/x-javascript
    </IfModule>
    
    # Cache headers for static assets
    <IfModule mod_expires.c>
        ExpiresActive On
        ExpiresByType image/jpg "access plus 1 month"
        ExpiresByType image/jpeg "access plus 1 month"
        ExpiresByType image/gif "access plus 1 month"
        ExpiresByType image/png "access plus 1 month"
        ExpiresByType image/webp "access plus 1 month"
        ExpiresByType text/css "access plus 1 month"
        ExpiresByType application/pdf "access plus 1 month"
        ExpiresByType application/javascript "access plus 1 month"
        ExpiresByType application/x-javascript "access plus 1 month"
        ExpiresByType application/x-shockwave-flash "access plus 1 month"
        ExpiresByType image/x-icon "access plus 1 year"
        ExpiresDefault "access plus 2 days"
    </IfModule>
    
    # Logging
    ErrorLog ${APACHE_LOG_DIR}/atv_gallery_error.log
    CustomLog ${APACHE_LOG_DIR}/atv_gallery_access.log combined
    
    # Set UTF-8 encoding
    AddDefaultCharset UTF-8
    
    # Disable server signature
    ServerSignature Off
</VirtualHost>

# SSL Configuration (recommended for production)
# <VirtualHost *:443>
#     ServerName your-domain.com
#     ServerAlias www.your-domain.com
#     DocumentRoot /path/to/atv_gallery/public
#     
#     SSLEngine on
#     SSLCertificateFile /path/to/your/certificate.crt
#     SSLCertificateKeyFile /path/to/your/private.key
#     SSLCertificateChainFile /path/to/your/chain.crt
#     
#     # Include the same directory and security configurations as above
# </VirtualHost>

# Redirect HTTP to HTTPS (for SSL setup)
# <VirtualHost *:80>
#     ServerName your-domain.com
#     ServerAlias www.your-domain.com
#     Redirect permanent / https://your-domain.com/
# </VirtualHost>
