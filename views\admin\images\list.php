<?php
$content = ob_start();
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h4 class="mb-0">إدارة الصور</h4>
    <a href="/admin/upload" class="btn btn-primary">
        <i class="fas fa-plus me-2"></i>رفع صور جديدة
    </a>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" action="/admin/images" class="row g-3">
            <div class="col-md-4">
                <label for="search" class="form-label">البحث</label>
                <input type="text" 
                       class="form-control" 
                       id="search" 
                       name="search" 
                       value="<?= htmlspecialchars($search) ?>"
                       placeholder="البحث في العنوان أو الوصف...">
            </div>
            <div class="col-md-4">
                <label for="category" class="form-label">الصنف</label>
                <select class="form-select" id="category" name="category">
                    <option value="">جميع الأصناف</option>
                    <?php foreach ($categories as $category): ?>
                        <option value="<?= $category['id'] ?>" 
                                <?= $selectedCategory == $category['id'] ? 'selected' : '' ?>>
                            <?= htmlspecialchars($category['name']) ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="col-md-4 d-flex align-items-end">
                <button type="submit" class="btn btn-outline-primary me-2">
                    <i class="fas fa-search me-1"></i>بحث
                </button>
                <a href="/admin/images" class="btn btn-outline-secondary">
                    <i class="fas fa-times me-1"></i>مسح
                </a>
            </div>
        </form>
    </div>
</div>

<?php if (empty($images['data'])): ?>
    <div class="card">
        <div class="card-body text-center py-5">
            <i class="fas fa-images fa-3x text-muted mb-3"></i>
            <h5>لا توجد صور</h5>
            <?php if (!empty($search) || !empty($selectedCategory)): ?>
                <p class="text-muted">لم يتم العثور على صور تطابق معايير البحث</p>
                <a href="/admin/images" class="btn btn-outline-primary">
                    <i class="fas fa-list me-2"></i>عرض جميع الصور
                </a>
            <?php else: ?>
                <p class="text-muted">ابدأ برفع صور جديدة</p>
                <a href="/admin/upload" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>رفع صور جديدة
                </a>
            <?php endif; ?>
        </div>
    </div>
<?php else: ?>
    <!-- Images Grid -->
    <div class="row">
        <?php foreach ($images['data'] as $image): ?>
            <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                <div class="card h-100">
                    <div class="position-relative">
                        <img src="/uploads/thumbnails/<?= htmlspecialchars($image['thumb_path']) ?>" 
                             class="card-img-top" 
                             alt="<?= htmlspecialchars($image['alt_text']) ?>"
                             style="height: 200px; object-fit: cover;">
                        
                        <!-- Action buttons overlay -->
                        <div class="position-absolute top-0 end-0 p-2">
                            <div class="btn-group-vertical btn-group-sm">
                                <a href="/uploads/images/<?= htmlspecialchars($image['file_path']) ?>" 
                                   class="btn btn-light btn-sm" 
                                   target="_blank"
                                   title="عرض بالحجم الكامل">
                                    <i class="fas fa-expand"></i>
                                </a>
                                <a href="/admin/images?action=edit&id=<?= $image['id'] ?>" 
                                   class="btn btn-primary btn-sm"
                                   title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button type="button" 
                                        class="btn btn-danger btn-sm"
                                        onclick="confirmDelete(<?= $image['id'] ?>, '<?= htmlspecialchars($image['title']) ?>')"
                                        title="حذف">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card-body">
                        <h6 class="card-title" title="<?= htmlspecialchars($image['title']) ?>">
                            <?= htmlspecialchars(mb_substr($image['title'], 0, 30)) ?>
                            <?= mb_strlen($image['title']) > 30 ? '...' : '' ?>
                        </h6>
                        
                        <?php if ($image['description']): ?>
                            <p class="card-text small text-muted">
                                <?= htmlspecialchars(mb_substr($image['description'], 0, 60)) ?>
                                <?= mb_strlen($image['description']) > 60 ? '...' : '' ?>
                            </p>
                        <?php endif; ?>
                        
                        <!-- Image meta info -->
                        <div class="small text-muted mb-2">
                            <?php if (isset($image['meta']['width']) && isset($image['meta']['height'])): ?>
                                <i class="fas fa-expand-arrows-alt me-1"></i>
                                <?= $image['meta']['width'] ?>×<?= $image['meta']['height'] ?>
                            <?php endif; ?>
                            
                            <?php if (isset($image['meta']['size'])): ?>
                                <br><i class="fas fa-file me-1"></i>
                                <?= number_format($image['meta']['size'] / 1024, 1) ?> KB
                            <?php endif; ?>
                        </div>
                        
                        <small class="text-muted">
                            <?= date('Y/m/d H:i', strtotime($image['created_at'])) ?>
                        </small>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    </div>
    
    <!-- Pagination -->
    <?php if ($images['last_page'] > 1): ?>
        <nav aria-label="صفحات الصور">
            <ul class="pagination justify-content-center">
                <?php if ($images['current_page'] > 1): ?>
                    <li class="page-item">
                        <a class="page-link" href="?page=<?= $images['current_page'] - 1 ?><?= !empty($search) ? '&search=' . urlencode($search) : '' ?><?= !empty($selectedCategory) ? '&category=' . $selectedCategory : '' ?>">
                            السابق
                        </a>
                    </li>
                <?php endif; ?>
                
                <?php for ($i = max(1, $images['current_page'] - 2); $i <= min($images['last_page'], $images['current_page'] + 2); $i++): ?>
                    <li class="page-item <?= $i == $images['current_page'] ? 'active' : '' ?>">
                        <a class="page-link" href="?page=<?= $i ?><?= !empty($search) ? '&search=' . urlencode($search) : '' ?><?= !empty($selectedCategory) ? '&category=' . $selectedCategory : '' ?>">
                            <?= $i ?>
                        </a>
                    </li>
                <?php endfor; ?>
                
                <?php if ($images['current_page'] < $images['last_page']): ?>
                    <li class="page-item">
                        <a class="page-link" href="?page=<?= $images['current_page'] + 1 ?><?= !empty($search) ? '&search=' . urlencode($search) : '' ?><?= !empty($selectedCategory) ? '&category=' . $selectedCategory : '' ?>">
                            التالي
                        </a>
                    </li>
                <?php endif; ?>
            </ul>
        </nav>
        
        <div class="text-center text-muted">
            عرض <?= number_format(($images['current_page'] - 1) * $images['per_page'] + 1) ?> - 
            <?= number_format(min($images['current_page'] * $images['per_page'], $images['total'])) ?> 
            من <?= number_format($images['total']) ?> صورة
        </div>
    <?php endif; ?>
<?php endif; ?>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف الصورة "<span id="imageName"></span>"؟</p>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>تحذير:</strong> سيتم حذف الصورة نهائياً من الخادم ولا يمكن استرجاعها.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <a href="#" id="deleteLink" class="btn btn-danger">
                    <i class="fas fa-trash me-2"></i>حذف نهائياً
                </a>
            </div>
        </div>
    </div>
</div>

<?php
$additionalJS = '
<script>
function confirmDelete(id, name) {
    document.getElementById("imageName").textContent = name;
    document.getElementById("deleteLink").href = "/admin/images?action=delete&id=" + id;
    new bootstrap.Modal(document.getElementById("deleteModal")).show();
}
</script>
';

$content = ob_get_clean();

// Include admin layout
$currentPage = 'images';
$pageTitle = 'إدارة الصور';
$breadcrumb = [
    ['title' => 'الرئيسية', 'url' => '/admin'],
    ['title' => 'الصور']
];

include VIEWS_PATH . 'layouts/admin.php';
?>
