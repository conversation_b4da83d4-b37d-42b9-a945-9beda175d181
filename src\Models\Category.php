<?php

namespace App\Models;

class Category extends BaseModel {
    protected $table = 'categories';
    protected $fillable = ['name', 'slug', 'description'];
    
    public function create($data) {
        // Generate slug if not provided
        if (empty($data['slug']) && !empty($data['name'])) {
            $data['slug'] = $this->generateUniqueSlug($data['name']);
        }
        
        return parent::create($data);
    }
    
    public function update($id, $data) {
        // Update slug if name changed
        if (!empty($data['name']) && (empty($data['slug']) || $data['slug'] === '')) {
            $data['slug'] = $this->generateUniqueSlug($data['name'], $id);
        }
        
        return parent::update($id, $data);
    }
    
    public function findBySlug($slug) {
        return $this->findBy('slug', $slug);
    }
    
    public function getImages($categoryId, $page = 1, $perPage = 12) {
        $offset = ($page - 1) * $perPage;
        
        $sql = "
            SELECT i.*, ci.created_at as attached_at
            FROM images i
            INNER JOIN category_image ci ON i.id = ci.image_id
            WHERE ci.category_id = ?
            ORDER BY ci.created_at DESC
            LIMIT {$perPage} OFFSET {$offset}
        ";
        
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([$categoryId]);
        $images = $stmt->fetchAll();
        
        // Get total count
        $countSql = "
            SELECT COUNT(*)
            FROM images i
            INNER JOIN category_image ci ON i.id = ci.image_id
            WHERE ci.category_id = ?
        ";
        $countStmt = $this->pdo->prepare($countSql);
        $countStmt->execute([$categoryId]);
        $total = $countStmt->fetchColumn();
        
        return [
            'data' => $images,
            'total' => $total,
            'per_page' => $perPage,
            'current_page' => $page,
            'last_page' => ceil($total / $perPage)
        ];
    }
    
    public function getImageCount($categoryId) {
        $stmt = $this->pdo->prepare("
            SELECT COUNT(*) 
            FROM category_image 
            WHERE category_id = ?
        ");
        $stmt->execute([$categoryId]);
        return $stmt->fetchColumn();
    }
    
    private function generateUniqueSlug($name, $excludeId = null) {
        $baseSlug = generateSlug($name);
        $slug = $baseSlug;
        $counter = 1;
        
        while ($this->slugExists($slug, $excludeId)) {
            $slug = $baseSlug . '-' . $counter;
            $counter++;
        }
        
        return $slug;
    }
    
    private function slugExists($slug, $excludeId = null) {
        $sql = "SELECT COUNT(*) FROM {$this->table} WHERE slug = ?";
        $params = [$slug];
        
        if ($excludeId) {
            $sql .= " AND id != ?";
            $params[] = $excludeId;
        }
        
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchColumn() > 0;
    }
}
