-- ATV Gallery Database Setup
-- Run this SQL in phpMyAdmin if PHP migrations don't work

-- Create database (if not exists)
CREATE DATABASE IF NOT EXISTS atv_gallery CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE atv_gallery;

-- Users table
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    email VARCHAR(255) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    name VARCHAR(255) NOT NULL,
    is_admin BOOLEAN DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Categories table
CREATE TABLE IF NOT EXISTS categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    slug VARCHAR(255) NOT NULL UNIQUE,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_slug (slug)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Images table
CREATE TABLE IF NOT EXISTS images (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    file_path VARCHAR(500) NOT NULL,
    thumb_path VARCHAR(500) NOT NULL,
    alt_text VARCHAR(255),
    meta JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_title (title),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Category-Image pivot table
CREATE TABLE IF NOT EXISTS category_image (
    category_id INT NOT NULL,
    image_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (category_id, image_id),
    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE CASCADE,
    FOREIGN KEY (image_id) REFERENCES images(id) ON DELETE CASCADE,
    INDEX idx_category_id (category_id),
    INDEX idx_image_id (image_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Migrations table
CREATE TABLE IF NOT EXISTS migrations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    migration VARCHAR(255) NOT NULL,
    executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert admin user
INSERT IGNORE INTO users (email, password, name, is_admin, created_at, updated_at) 
VALUES ('<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'مدير النظام', 1, NOW(), NOW());
-- Password is: admin123

-- Insert sample categories
INSERT IGNORE INTO categories (name, slug, description, created_at, updated_at) VALUES
('طبيعة', 'nature', 'صور طبيعية خلابة من مناظر طبيعية وحدائق ومناظر جبلية', NOW(), NOW()),
('مدن', 'cities', 'صور من المدن والمباني والعمارة الحديثة والتراثية', NOW(), NOW()),
('أشخاص', 'people', 'صور شخصية وجماعية ولقطات من الحياة اليومية', NOW(), NOW()),
('طعام', 'food', 'صور للأطعمة والمشروبات والوصفات', NOW(), NOW()),
('تقنية', 'technology', 'صور للأجهزة التقنية والابتكارات والتطبيقات', NOW(), NOW()),
('رياضة', 'sports', 'صور رياضية من مختلف الألعاب والأنشطة الرياضية', NOW(), NOW()),
('فن', 'art', 'أعمال فنية ولوحات ومنحوتات وإبداعات فنية', NOW(), NOW()),
('سفر', 'travel', 'صور من الرحلات والسفر والمعالم السياحية', NOW(), NOW());

-- Record migrations
INSERT IGNORE INTO migrations (migration) VALUES
('001_create_users_table'),
('002_create_categories_table'),
('003_create_images_table'),
('004_create_category_image_table');
