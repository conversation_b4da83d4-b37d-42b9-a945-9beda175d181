<?php

class CreateImagesTable {
    
    public static function up($pdo) {
        $sql = "
            CREATE TABLE IF NOT EXISTS images (
                id INT AUTO_INCREMENT PRIMARY KEY,
                title VARCHAR(255) NOT NULL,
                description TEXT,
                file_path VARCHAR(500) NOT NULL,
                thumb_path VARCHAR(500) NOT NULL,
                alt_text VARCHAR(255),
                meta JSON,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_title (title),
                INDEX idx_created_at (created_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        
        $pdo->exec($sql);
        echo "Images table created successfully.\n";
    }
    
    public static function down($pdo) {
        $sql = "DROP TABLE IF EXISTS images";
        $pdo->exec($sql);
        echo "Images table dropped successfully.\n";
    }
}
