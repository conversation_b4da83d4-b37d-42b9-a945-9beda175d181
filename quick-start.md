# دليل البدء السريع - ATV Gallery

## التثبيت السريع (5 دقائق)

### 1. المتطلبات
- XAMPP أو WAMP أو LAMP
- PHP 8.2+
- Composer

### 2. التحميل والإعداد
```bash
# انسخ الملفات إلى مجلد htdocs
cd /path/to/xampp/htdocs
# أو انسخ المجلد يدوياً

# ادخل إلى مجلد المشروع
cd atv_galary
```

### 3. التثبيت التلقائي
```bash
# تشغيل سكريبت التثبيت
php install.php
```

### 4. إعداد قاعدة البيانات
1. افتح phpMyAdmin
2. أنشئ قاعدة بيانات جديدة: `atv_gallery`
3. عدّل ملف `.env`:
```env
DB_HOST=localhost
DB_DATABASE=atv_gallery
DB_USERNAME=root
DB_PASSWORD=
```

### 5. تشغيل المايجريشن والسيدر
```bash
php migrate.php
php seed.php
```

### 6. الوصول للنظام
- الموقع: `http://localhost/atv_galary/public`
- الإدارة: `http://localhost/atv_galary/public/login`
- البريد: `<EMAIL>`
- كلمة المرور: `admin123`

## التشغيل السريع للتطوير

```bash
# تشغيل خادم PHP المدمج
php serve.php localhost 8000

# ثم افتح المتصفح على
# http://localhost:8000
```

## الخطوات الأولى

### 1. تسجيل الدخول
- اذهب إلى `/login`
- استخدم البيانات الافتراضية
- **مهم**: غيّر كلمة المرور فوراً

### 2. إنشاء الأصناف
- اذهب إلى "الأصناف" في لوحة الإدارة
- أضف أصناف مثل: طبيعة، مدن، أشخاص

### 3. رفع الصور
- اذهب إلى "رفع صور"
- اسحب الصور أو انقر للاختيار
- اختر الأصناف المناسبة
- انقر "رفع الصور"

### 4. معاينة المعرض
- اذهب إلى الصفحة الرئيسية
- تصفح الصور والأصناف
- جرب البحث والفلترة

## نصائح مهمة

### الأمان
- غيّر كلمة مرور المشرف
- احذف المستخدم التجريبي
- راجع إعدادات `.env`

### الأداء
- فعّل mod_rewrite في Apache
- استخدم HTTPS في الإنتاج
- فعّل الضغط والتخزين المؤقت

### النسخ الاحتياطي
- انسخ مجلد `uploads` بانتظام
- اعمل نسخة احتياطية من قاعدة البيانات
- احتفظ بنسخة من ملف `.env`

## حل المشاكل الشائعة

### خطأ 500
- تحقق من صلاحيات المجلدات
- تأكد من تفعيل mod_rewrite
- راجع ملف error.log

### الصور لا تظهر
- تحقق من مسار uploads
- تأكد من صلاحيات الكتابة
- راجع إعدادات PHP (upload_max_filesize)

### مشاكل قاعدة البيانات
- تحقق من إعدادات `.env`
- تأكد من تشغيل MySQL
- راجع اسم قاعدة البيانات

## الدعم

- راجع ملف `README.md` للتفاصيل الكاملة
- تحقق من ملف `CHANGELOG.md` للتحديثات
- أنشئ Issue في GitHub للمشاكل

---

**مبروك! معرض الصور جاهز للاستخدام 🎉**
