<?php
/**
 * ATV Gallery Installation Script
 * This script helps with the initial setup of the gallery system
 */

// Check if running from command line
if (php_sapi_name() !== 'cli') {
    die('This script must be run from the command line.');
}

echo "=== ATV Gallery Installation ===\n\n";

// Check PHP version
if (version_compare(PHP_VERSION, '8.2.0', '<')) {
    die("Error: PHP 8.2 or higher is required. Current version: " . PHP_VERSION . "\n");
}

// Check required extensions
$requiredExtensions = ['pdo', 'pdo_mysql', 'gd', 'json', 'mbstring'];
$missingExtensions = [];

foreach ($requiredExtensions as $ext) {
    if (!extension_loaded($ext)) {
        $missingExtensions[] = $ext;
    }
}

if (!empty($missingExtensions)) {
    die("Error: Missing required PHP extensions: " . implode(', ', $missingExtensions) . "\n");
}

echo "✓ PHP version check passed\n";
echo "✓ Required extensions check passed\n\n";

// Check if .env exists
if (!file_exists('.env')) {
    if (file_exists('.env.example')) {
        copy('.env.example', '.env');
        echo "✓ Created .env file from .env.example\n";
    } else {
        die("Error: .env.example file not found\n");
    }
} else {
    echo "✓ .env file already exists\n";
}

// Check if vendor directory exists
if (!is_dir('vendor')) {
    echo "Installing Composer dependencies...\n";
    exec('composer install --no-dev --optimize-autoloader', $output, $returnCode);
    if ($returnCode !== 0) {
        die("Error: Failed to install Composer dependencies. Please run 'composer install' manually.\n");
    }
    echo "✓ Composer dependencies installed\n";
} else {
    echo "✓ Composer dependencies already installed\n";
}

// Create necessary directories
$directories = [
    'public/uploads/images',
    'public/uploads/thumbnails',
    'storage/logs'
];

foreach ($directories as $dir) {
    if (!is_dir($dir)) {
        if (mkdir($dir, 0755, true)) {
            echo "✓ Created directory: $dir\n";
        } else {
            echo "✗ Failed to create directory: $dir\n";
        }
    } else {
        echo "✓ Directory already exists: $dir\n";
    }
}

// Set permissions
$permissionDirs = [
    'public/uploads' => 0755,
    'storage' => 0755
];

foreach ($permissionDirs as $dir => $permission) {
    if (is_dir($dir)) {
        chmod($dir, $permission);
        echo "✓ Set permissions for: $dir\n";
    }
}

echo "\n=== Database Setup ===\n";

// Load environment variables for database setup
if (file_exists('.env')) {
    $envContent = file_get_contents('.env');
    $envLines = explode("\n", $envContent);
    
    foreach ($envLines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value);
        }
    }
}

// Check database configuration
$dbHost = $_ENV['DB_HOST'] ?? 'localhost';
$dbName = $_ENV['DB_DATABASE'] ?? 'atv_gallery';
$dbUser = $_ENV['DB_USERNAME'] ?? 'root';
$dbPass = $_ENV['DB_PASSWORD'] ?? '';

echo "Database Host: $dbHost\n";
echo "Database Name: $dbName\n";
echo "Database User: $dbUser\n";

// Test database connection
try {
    $dsn = "mysql:host=$dbHost;charset=utf8mb4";
    $pdo = new PDO($dsn, $dbUser, $dbPass, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
    ]);
    
    // Create database if it doesn't exist
    $pdo->exec("CREATE DATABASE IF NOT EXISTS `$dbName` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "✓ Database '$dbName' created or already exists\n";
    
} catch (PDOException $e) {
    die("Error: Could not connect to database. Please check your .env configuration.\n" . $e->getMessage() . "\n");
}

echo "\n=== Running Migrations ===\n";

// Run migrations
require_once 'migrate.php';

echo "\n=== Seeding Database ===\n";

// Run seeder
require_once 'seed.php';

echo "\n=== Installation Complete! ===\n\n";

echo "🎉 ATV Gallery has been successfully installed!\n\n";

echo "Next steps:\n";
echo "1. Configure your web server to point to the 'public' directory\n";
echo "2. Ensure mod_rewrite is enabled (Apache) or configure URL rewriting (Nginx)\n";
echo "3. Visit your website and login to the admin panel:\n";
echo "   - URL: http://your-domain/login\n";
echo "   - Email: <EMAIL>\n";
echo "   - Password: admin123\n\n";

echo "Important:\n";
echo "- Change the default admin password after first login\n";
echo "- Review the .env file and update settings as needed\n";
echo "- Make sure the uploads directory is writable\n\n";

echo "For more information, see README.md\n";
echo "Enjoy your new photo gallery! 📸\n";
?>
