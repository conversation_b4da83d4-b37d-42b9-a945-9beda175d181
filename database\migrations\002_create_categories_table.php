<?php

class CreateCategoriesTable {
    
    public static function up($pdo) {
        $sql = "
            CREATE TABLE IF NOT EXISTS categories (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                slug VARCHAR(255) NOT NULL UNIQUE,
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_slug (slug)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        
        $pdo->exec($sql);
        echo "Categories table created successfully.\n";
    }
    
    public static function down($pdo) {
        $sql = "DROP TABLE IF EXISTS categories";
        $pdo->exec($sql);
        echo "Categories table dropped successfully.\n";
    }
}
