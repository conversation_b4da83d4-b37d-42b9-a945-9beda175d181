<?php

class CreateUsersTable {
    
    public static function up($pdo) {
        $sql = "
            CREATE TABLE IF NOT EXISTS users (
                id INT AUTO_INCREMENT PRIMARY KEY,
                email VARCHAR(255) NOT NULL UNIQUE,
                password VARCHAR(255) NOT NULL,
                name VA<PERSON>HAR(255) NOT NULL,
                is_admin BOOLEAN DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        
        $pdo->exec($sql);
        echo "Users table created successfully.\n";
    }
    
    public static function down($pdo) {
        $sql = "DROP TABLE IF EXISTS users";
        $pdo->exec($sql);
        echo "Users table dropped successfully.\n";
    }
}
