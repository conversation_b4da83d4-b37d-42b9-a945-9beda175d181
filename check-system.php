<?php
/**
 * ATV Gallery System Check
 * Verifies system requirements and configuration
 */

echo "=== ATV Gallery System Check ===\n\n";

$errors = [];
$warnings = [];
$success = [];

// Check PHP version
if (version_compare(PHP_VERSION, '8.2.0', '>=')) {
    $success[] = "✓ PHP version: " . PHP_VERSION;
} else {
    $errors[] = "✗ PHP version: " . PHP_VERSION . " (Required: 8.2+)";
}

// Check required extensions
$requiredExtensions = [
    'pdo' => 'PDO Database Support',
    'pdo_mysql' => 'MySQL PDO Driver',
    'gd' => 'GD Image Processing',
    'json' => 'JSON Support',
    'mbstring' => 'Multibyte String Support',
    'fileinfo' => 'File Information',
    'openssl' => 'OpenSSL Support'
];

foreach ($requiredExtensions as $ext => $description) {
    if (extension_loaded($ext)) {
        $success[] = "✓ $description";
    } else {
        $errors[] = "✗ Missing extension: $ext ($description)";
    }
}

// Check optional extensions
$optionalExtensions = [
    'imagick' => 'ImageMagick (Alternative to GD)',
    'exif' => 'EXIF Data Reading',
    'curl' => 'cURL Support',
    'zip' => 'ZIP Archive Support'
];

foreach ($optionalExtensions as $ext => $description) {
    if (extension_loaded($ext)) {
        $success[] = "✓ $description (Optional)";
    } else {
        $warnings[] = "! Missing optional extension: $ext ($description)";
    }
}

// Check PHP settings
$phpSettings = [
    'file_uploads' => ['On', 'File uploads must be enabled'],
    'upload_max_filesize' => ['10M', 'Should be at least 10M for image uploads'],
    'post_max_size' => ['50M', 'Should be larger than upload_max_filesize'],
    'max_execution_time' => ['300', 'Should be at least 300 seconds for large uploads'],
    'memory_limit' => ['256M', 'Should be at least 256M for image processing']
];

foreach ($phpSettings as $setting => $info) {
    $value = ini_get($setting);
    $recommended = $info[0];
    $description = $info[1];
    
    if ($setting === 'file_uploads') {
        if ($value) {
            $success[] = "✓ $setting: Enabled";
        } else {
            $errors[] = "✗ $setting: Disabled ($description)";
        }
    } else {
        $success[] = "✓ $setting: $value (Recommended: $recommended)";
        
        // Convert to bytes for comparison
        $valueBytes = convertToBytes($value);
        $recommendedBytes = convertToBytes($recommended);
        
        if ($valueBytes < $recommendedBytes) {
            $warnings[] = "! $setting: $value is less than recommended $recommended ($description)";
        }
    }
}

// Check directories and permissions
$directories = [
    'public/uploads/images' => 'Image uploads directory',
    'public/uploads/thumbnails' => 'Thumbnail directory',
    'storage/logs' => 'Log directory',
    'vendor' => 'Composer dependencies'
];

foreach ($directories as $dir => $description) {
    if (is_dir($dir)) {
        if (is_writable($dir)) {
            $success[] = "✓ $description: Exists and writable";
        } else {
            $warnings[] = "! $description: Exists but not writable";
        }
    } else {
        $warnings[] = "! $description: Does not exist";
    }
}

// Check important files
$files = [
    '.env' => 'Environment configuration',
    'composer.json' => 'Composer configuration',
    'public/.htaccess' => 'Apache rewrite rules',
    'public/index.php' => 'Main entry point'
];

foreach ($files as $file => $description) {
    if (file_exists($file)) {
        $success[] = "✓ $description: Exists";
    } else {
        if ($file === '.env') {
            $errors[] = "✗ $description: Missing (copy from .env.example)";
        } else {
            $warnings[] = "! $description: Missing";
        }
    }
}

// Check database connection (if .env exists)
if (file_exists('.env')) {
    $envContent = file_get_contents('.env');
    $envLines = explode("\n", $envContent);
    
    foreach ($envLines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value);
        }
    }
    
    $dbHost = $_ENV['DB_HOST'] ?? 'localhost';
    $dbName = $_ENV['DB_DATABASE'] ?? 'atv_gallery';
    $dbUser = $_ENV['DB_USERNAME'] ?? 'root';
    $dbPass = $_ENV['DB_PASSWORD'] ?? '';
    
    try {
        $dsn = "mysql:host=$dbHost;dbname=$dbName;charset=utf8mb4";
        $pdo = new PDO($dsn, $dbUser, $dbPass, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
        ]);
        $success[] = "✓ Database connection: Successful";
        
        // Check if tables exist
        $tables = ['users', 'categories', 'images', 'category_image'];
        foreach ($tables as $table) {
            $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
            if ($stmt->rowCount() > 0) {
                $success[] = "✓ Database table '$table': Exists";
            } else {
                $warnings[] = "! Database table '$table': Missing (run migrations)";
            }
        }
        
    } catch (PDOException $e) {
        $errors[] = "✗ Database connection: Failed (" . $e->getMessage() . ")";
    }
}

// Display results
echo "=== SUCCESS ===\n";
foreach ($success as $item) {
    echo "$item\n";
}

if (!empty($warnings)) {
    echo "\n=== WARNINGS ===\n";
    foreach ($warnings as $item) {
        echo "$item\n";
    }
}

if (!empty($errors)) {
    echo "\n=== ERRORS ===\n";
    foreach ($errors as $item) {
        echo "$item\n";
    }
}

echo "\n=== SUMMARY ===\n";
echo "Success: " . count($success) . "\n";
echo "Warnings: " . count($warnings) . "\n";
echo "Errors: " . count($errors) . "\n";

if (empty($errors)) {
    echo "\n🎉 System check passed! ATV Gallery should work properly.\n";
    if (!empty($warnings)) {
        echo "⚠️  Please review the warnings above for optimal performance.\n";
    }
} else {
    echo "\n❌ System check failed! Please fix the errors above before proceeding.\n";
    exit(1);
}

// Helper function to convert PHP size values to bytes
function convertToBytes($value) {
    $value = trim($value);
    $last = strtolower($value[strlen($value)-1]);
    $value = (int) $value;
    
    switch($last) {
        case 'g':
            $value *= 1024;
        case 'm':
            $value *= 1024;
        case 'k':
            $value *= 1024;
    }
    
    return $value;
}
?>
