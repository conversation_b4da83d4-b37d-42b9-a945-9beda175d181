<?php

require_once __DIR__ . '/vendor/autoload.php';
require_once __DIR__ . '/src/config.php';
require_once __DIR__ . '/database/seeders/DatabaseSeeder.php';

if (php_sapi_name() !== 'cli') {
    die('This script can only be run from the command line.');
}

try {
    $seeder = new DatabaseSeeder();
    $seeder->run();
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    exit(1);
}
