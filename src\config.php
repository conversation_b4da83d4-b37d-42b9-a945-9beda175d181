<?php

// Load environment variables
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__ . '/..');
$dotenv->load();

// Database configuration
define('DB_HOST', $_ENV['DB_HOST'] ?? 'localhost');
define('DB_PORT', $_ENV['DB_PORT'] ?? '3306');
define('DB_DATABASE', $_ENV['DB_DATABASE'] ?? 'atv_gallery');
define('DB_USERNAME', $_ENV['DB_USERNAME'] ?? 'root');
define('DB_PASSWORD', $_ENV['DB_PASSWORD'] ?? '');

// Application configuration
define('APP_NAME', $_ENV['APP_NAME'] ?? 'ATV Gallery');
define('APP_URL', $_ENV['APP_URL'] ?? 'http://localhost');
define('APP_DEBUG', $_ENV['APP_DEBUG'] ?? true);
define('APP_ENV', $_ENV['APP_ENV'] ?? 'local');

// Upload configuration
define('UPLOAD_MAX_SIZE', $_ENV['UPLOAD_MAX_SIZE'] ?? 10485760); // 10MB
define('THUMBNAIL_WIDTH', $_ENV['THUMBNAIL_WIDTH'] ?? 600);
define('THUMBNAIL_HEIGHT', $_ENV['THUMBNAIL_HEIGHT'] ?? 400);

// Paths
define('UPLOAD_PATH', __DIR__ . '/../public/uploads/images/');
define('THUMBNAIL_PATH', __DIR__ . '/../public/uploads/thumbnails/');
define('VIEWS_PATH', __DIR__ . '/../views/');

// Session configuration
ini_set('session.cookie_lifetime', $_ENV['SESSION_LIFETIME'] ?? 7200);
ini_set('session.cookie_secure', $_ENV['SESSION_SECURE'] ?? false);
ini_set('session.cookie_httponly', $_ENV['SESSION_HTTP_ONLY'] ?? true);

// Security
define('APP_KEY', $_ENV['APP_KEY'] ?? 'your-secret-key-here');

// Database connection
class Database {
    private static $connection = null;
    
    public static function getConnection() {
        if (self::$connection === null) {
            try {
                $dsn = "mysql:host=" . DB_HOST . ";port=" . DB_PORT . ";dbname=" . DB_DATABASE . ";charset=utf8mb4";
                self::$connection = new PDO($dsn, DB_USERNAME, DB_PASSWORD, [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::ATTR_EMULATE_PREPARES => false,
                ]);
            } catch (PDOException $e) {
                if (APP_DEBUG) {
                    die("Database connection failed: " . $e->getMessage());
                } else {
                    die("Database connection failed");
                }
            }
        }
        return self::$connection;
    }
}

// Helper functions
function view($template, $data = []) {
    extract($data);
    $templatePath = VIEWS_PATH . $template . '.php';
    if (file_exists($templatePath)) {
        include $templatePath;
    } else {
        throw new Exception("Template not found: " . $template);
    }
}

function redirect($url) {
    header("Location: " . $url);
    exit;
}

function isLoggedIn() {
    return isset($_SESSION['user_id']);
}

function isAdmin() {
    return isset($_SESSION['is_admin']) && $_SESSION['is_admin'] == 1;
}

function generateSlug($text) {
    // Convert Arabic and English text to URL-friendly slug
    $text = trim($text);
    $text = preg_replace('/[^\p{L}\p{N}\s\-_]/u', '', $text);
    $text = preg_replace('/[\s\-_]+/', '-', $text);
    $text = trim($text, '-');
    return strtolower($text);
}
