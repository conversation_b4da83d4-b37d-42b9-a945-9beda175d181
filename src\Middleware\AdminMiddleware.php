<?php

namespace App\Middleware;

class AdminMiddleware {
    
    public static function check() {
        // Check if user is logged in
        if (!isLoggedIn()) {
            redirect('/login?redirect=' . urlencode($_SERVER['REQUEST_URI']));
        }
        
        // Check if user is admin
        if (!isAdmin()) {
            http_response_code(403);
            echo "Access denied. Admin privileges required.";
            exit;
        }
    }
}
