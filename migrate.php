<?php

require_once __DIR__ . '/vendor/autoload.php';
require_once __DIR__ . '/src/config.php';

class Migrator {
    private $pdo;
    private $migrationsPath;
    
    public function __construct() {
        $this->pdo = Database::getConnection();
        $this->migrationsPath = __DIR__ . '/database/migrations/';
        $this->createMigrationsTable();
    }
    
    private function createMigrationsTable() {
        $sql = "
            CREATE TABLE IF NOT EXISTS migrations (
                id INT AUTO_INCREMENT PRIMARY KEY,
                migration VARCHAR(255) NOT NULL,
                executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        $this->pdo->exec($sql);
    }
    
    public function migrate() {
        $files = glob($this->migrationsPath . '*.php');
        sort($files);
        
        foreach ($files as $file) {
            $migrationName = basename($file, '.php');
            
            // Check if migration already executed
            $stmt = $this->pdo->prepare("SELECT COUNT(*) FROM migrations WHERE migration = ?");
            $stmt->execute([$migrationName]);
            
            if ($stmt->fetchColumn() == 0) {
                echo "Running migration: $migrationName\n";
                
                require_once $file;
                $className = $this->getClassNameFromFile($migrationName);
                
                if (class_exists($className)) {
                    $className::up($this->pdo);
                    
                    // Record migration
                    $stmt = $this->pdo->prepare("INSERT INTO migrations (migration) VALUES (?)");
                    $stmt->execute([$migrationName]);
                    
                    echo "Migration $migrationName completed.\n";
                } else {
                    echo "Class $className not found in $migrationName\n";
                }
            } else {
                echo "Migration $migrationName already executed.\n";
            }
        }
        
        echo "All migrations completed.\n";
    }
    
    public function rollback() {
        $stmt = $this->pdo->query("SELECT migration FROM migrations ORDER BY id DESC LIMIT 1");
        $lastMigration = $stmt->fetchColumn();
        
        if ($lastMigration) {
            $file = $this->migrationsPath . $lastMigration . '.php';
            if (file_exists($file)) {
                require_once $file;
                $className = $this->getClassNameFromFile($lastMigration);
                
                if (class_exists($className)) {
                    echo "Rolling back migration: $lastMigration\n";
                    $className::down($this->pdo);
                    
                    // Remove migration record
                    $stmt = $this->pdo->prepare("DELETE FROM migrations WHERE migration = ?");
                    $stmt->execute([$lastMigration]);
                    
                    echo "Rollback completed.\n";
                }
            }
        } else {
            echo "No migrations to rollback.\n";
        }
    }
    
    private function getClassNameFromFile($filename) {
        // Convert filename like "001_create_users_table" to "CreateUsersTable"
        $parts = explode('_', $filename);
        array_shift($parts); // Remove number prefix
        return implode('', array_map('ucfirst', $parts));
    }
}

// Command line interface
if (php_sapi_name() === 'cli') {
    $migrator = new Migrator();
    
    $command = $argv[1] ?? 'migrate';
    
    switch ($command) {
        case 'migrate':
            $migrator->migrate();
            break;
        case 'rollback':
            $migrator->rollback();
            break;
        default:
            echo "Usage: php migrate.php [migrate|rollback]\n";
            break;
    }
}
