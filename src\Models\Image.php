<?php

namespace App\Models;

class Image extends BaseModel {
    protected $table = 'images';
    protected $fillable = ['title', 'description', 'file_path', 'thumb_path', 'alt_text', 'meta'];
    
    public function create($data) {
        // Encode meta data as JSON if it's an array
        if (isset($data['meta']) && is_array($data['meta'])) {
            $data['meta'] = json_encode($data['meta']);
        }
        
        return parent::create($data);
    }
    
    public function update($id, $data) {
        // Encode meta data as JSON if it's an array
        if (isset($data['meta']) && is_array($data['meta'])) {
            $data['meta'] = json_encode($data['meta']);
        }
        
        return parent::update($id, $data);
    }
    
    public function find($id) {
        $image = parent::find($id);
        if ($image && $image['meta']) {
            $image['meta'] = json_decode($image['meta'], true);
        }
        return $image;
    }
    
    public function all($orderBy = 'created_at', $direction = 'DESC') {
        $images = parent::all($orderBy, $direction);
        foreach ($images as &$image) {
            if ($image['meta']) {
                $image['meta'] = json_decode($image['meta'], true);
            }
        }
        return $images;
    }
    
    public function paginate($page = 1, $perPage = 12, $orderBy = 'created_at', $direction = 'DESC') {
        $result = parent::paginate($page, $perPage, $orderBy, $direction);
        
        foreach ($result['data'] as &$image) {
            if ($image['meta']) {
                $image['meta'] = json_decode($image['meta'], true);
            }
        }
        
        return $result;
    }
    
    public function search($term, $page = 1, $perPage = 12) {
        $offset = ($page - 1) * $perPage;
        
        $sql = "
            SELECT * FROM {$this->table} 
            WHERE title LIKE ? OR description LIKE ? OR alt_text LIKE ?
            ORDER BY created_at DESC
            LIMIT {$perPage} OFFSET {$offset}
        ";
        
        $searchTerm = "%{$term}%";
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([$searchTerm, $searchTerm, $searchTerm]);
        $images = $stmt->fetchAll();
        
        // Decode meta for each image
        foreach ($images as &$image) {
            if ($image['meta']) {
                $image['meta'] = json_decode($image['meta'], true);
            }
        }
        
        // Get total count
        $countSql = "
            SELECT COUNT(*) FROM {$this->table} 
            WHERE title LIKE ? OR description LIKE ? OR alt_text LIKE ?
        ";
        $countStmt = $this->pdo->prepare($countSql);
        $countStmt->execute([$searchTerm, $searchTerm, $searchTerm]);
        $total = $countStmt->fetchColumn();
        
        return [
            'data' => $images,
            'total' => $total,
            'per_page' => $perPage,
            'current_page' => $page,
            'last_page' => ceil($total / $perPage)
        ];
    }
    
    public function getCategories($imageId) {
        $sql = "
            SELECT c.*
            FROM categories c
            INNER JOIN category_image ci ON c.id = ci.category_id
            WHERE ci.image_id = ?
            ORDER BY c.name
        ";
        
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([$imageId]);
        return $stmt->fetchAll();
    }
    
    public function attachToCategory($imageId, $categoryId) {
        $sql = "INSERT IGNORE INTO category_image (image_id, category_id) VALUES (?, ?)";
        $stmt = $this->pdo->prepare($sql);
        return $stmt->execute([$imageId, $categoryId]);
    }
    
    public function detachFromCategory($imageId, $categoryId) {
        $sql = "DELETE FROM category_image WHERE image_id = ? AND category_id = ?";
        $stmt = $this->pdo->prepare($sql);
        return $stmt->execute([$imageId, $categoryId]);
    }
    
    public function syncCategories($imageId, $categoryIds) {
        // Remove all existing associations
        $stmt = $this->pdo->prepare("DELETE FROM category_image WHERE image_id = ?");
        $stmt->execute([$imageId]);
        
        // Add new associations
        if (!empty($categoryIds)) {
            $sql = "INSERT INTO category_image (image_id, category_id) VALUES ";
            $values = [];
            $params = [];
            
            foreach ($categoryIds as $categoryId) {
                $values[] = "(?, ?)";
                $params[] = $imageId;
                $params[] = $categoryId;
            }
            
            $sql .= implode(', ', $values);
            $stmt = $this->pdo->prepare($sql);
            return $stmt->execute($params);
        }
        
        return true;
    }
    
    public function delete($id) {
        // Get image info before deletion
        $image = $this->find($id);
        
        if ($image) {
            // Delete files
            if (file_exists(UPLOAD_PATH . $image['file_path'])) {
                unlink(UPLOAD_PATH . $image['file_path']);
            }
            if (file_exists(THUMBNAIL_PATH . $image['thumb_path'])) {
                unlink(THUMBNAIL_PATH . $image['thumb_path']);
            }
        }
        
        return parent::delete($id);
    }
}
