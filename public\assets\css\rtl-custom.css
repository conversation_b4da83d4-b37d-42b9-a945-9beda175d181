/* RTL Custom Styles for ATV Gallery */

/* Arabic Font Support */
body {
    font-family: 'Segoe UI', 'Tahoma', 'Arial', 'Helvetica Neue', sans-serif;
}

/* RTL Specific Adjustments */
.rtl-flip {
    transform: scaleX(-1);
}

/* Navigation RTL */
.navbar-nav .nav-link {
    padding-right: 0.75rem;
    padding-left: 0.75rem;
}

/* Breadcrumb RTL */
.breadcrumb-item + .breadcrumb-item::before {
    content: "‹";
    float: left;
    padding-right: 0;
    padding-left: 0.5rem;
}

/* Form Controls RTL */
.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* Button Groups RTL */
.btn-group > .btn:not(:last-child):not(.dropdown-toggle) {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-top-left-radius: 0.375rem;
    border-bottom-left-radius: 0.375rem;
}

.btn-group > .btn:not(:first-child) {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    border-top-right-radius: 0.375rem;
    border-bottom-right-radius: 0.375rem;
}

/* Pagination RTL */
.pagination .page-link {
    text-align: center;
}

/* Modal RTL */
.modal-header .btn-close {
    margin: -0.5rem auto -0.5rem -0.5rem;
}

/* Alert RTL */
.alert-dismissible .btn-close {
    position: absolute;
    top: 0;
    left: 0;
    right: auto;
    z-index: 2;
    padding: 1.25rem 1rem;
}

/* Dropdown RTL */
.dropdown-menu {
    text-align: right;
}

/* Card RTL */
.card-header {
    text-align: right;
}

/* Table RTL */
.table th,
.table td {
    text-align: right;
}

.table th:first-child,
.table td:first-child {
    border-right: 0;
}

.table th:last-child,
.table td:last-child {
    border-left: 0;
}

/* Input Group RTL */
.input-group > .form-control:not(:last-child),
.input-group > .form-select:not(:last-child) {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-top-left-radius: 0.375rem;
    border-bottom-left-radius: 0.375rem;
}

.input-group > .form-control:not(:first-child),
.input-group > .form-select:not(:first-child) {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    border-top-right-radius: 0.375rem;
    border-bottom-right-radius: 0.375rem;
}

/* Progress RTL */
.progress-bar {
    transition: width 0.6s ease;
}

/* List Group RTL */
.list-group-item {
    text-align: right;
}

/* Badge RTL */
.badge {
    text-align: center;
}

/* Tooltip RTL */
.tooltip-inner {
    text-align: center;
}

/* Popover RTL */
.popover-body {
    text-align: right;
}

/* Carousel RTL */
.carousel-control-prev {
    left: auto;
    right: 0;
}

.carousel-control-next {
    right: auto;
    left: 0;
}

.carousel-control-prev-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23fff'%3e%3cpath d='m11.354 1.646a.5.5 0 0 1 0 .708L5.707 8l5.647 5.646a.5.5 0 0 1-.708.708l-6-6a.5.5 0 0 1 0-.708l6-6a.5.5 0 0 1 .708 0z'/%3e%3c/svg%3e");
}

.carousel-control-next-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23fff'%3e%3cpath d='m4.646 1.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1 0 .708l-6 6a.5.5 0 0 1-.708-.708L10.293 8 4.646 2.354a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
}

/* Custom Gallery Styles */
.category-btn {
    font-weight: 500;
    white-space: nowrap;
}

.image-card {
    overflow: hidden;
}

.masonry-grid {
    direction: ltr; /* Keep images in natural flow */
}

.masonry-item {
    direction: rtl; /* But keep text RTL */
}

/* Admin Panel RTL Adjustments */
.sidebar .nav-link.active {
    transform: translateX(5px); /* Reverse the transform for RTL */
}

.sidebar .nav-link:hover {
    transform: translateX(5px); /* Reverse the transform for RTL */
}

/* Upload Area RTL */
.upload-area {
    text-align: center;
}

.preview-item .remove-btn {
    right: auto;
    left: 5px;
}

/* Search Box RTL */
.search-box {
    text-align: right;
}

.search-box::placeholder {
    text-align: right;
}

/* Lightbox RTL */
.lb-data .lb-caption {
    text-align: center;
    direction: rtl;
}

.lb-data .lb-number {
    direction: ltr;
}

/* Responsive RTL Adjustments */
@media (max-width: 768px) {
    .navbar-collapse {
        text-align: right;
    }
    
    .hero-section {
        text-align: center;
    }
    
    .category-filter {
        text-align: center;
    }
    
    .masonry-grid {
        column-count: 1;
    }
}

/* Print Styles RTL */
@media print {
    body {
        direction: rtl;
        text-align: right;
    }
    
    .sidebar,
    .navbar,
    .footer {
        display: none !important;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .btn {
        border-width: 2px;
    }
    
    .card {
        border-width: 2px;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    .image-card,
    .category-btn,
    .btn,
    * {
        transition: none !important;
        animation: none !important;
    }
}
