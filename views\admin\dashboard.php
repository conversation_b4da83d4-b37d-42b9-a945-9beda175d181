<?php
$content = ob_start();
?>

<div class="row mb-4">
    <div class="col-md-4">
        <div class="card text-center">
            <div class="card-body">
                <div class="display-4 text-primary mb-3">
                    <i class="fas fa-images"></i>
                </div>
                <h3 class="card-title"><?= number_format($total_images) ?></h3>
                <p class="card-text text-muted">إجمالي الصور</p>
                <a href="/admin/images" class="btn btn-outline-primary">
                    <i class="fas fa-eye me-2"></i>عرض الصور
                </a>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card text-center">
            <div class="card-body">
                <div class="display-4 text-success mb-3">
                    <i class="fas fa-tags"></i>
                </div>
                <h3 class="card-title"><?= number_format($total_categories) ?></h3>
                <p class="card-text text-muted">إجمالي الأصناف</p>
                <a href="/admin/categories" class="btn btn-outline-success">
                    <i class="fas fa-eye me-2"></i>عرض الأصناف
                </a>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card text-center">
            <div class="card-body">
                <div class="display-4 text-info mb-3">
                    <i class="fas fa-cloud-upload-alt"></i>
                </div>
                <h3 class="card-title">رفع جديد</h3>
                <p class="card-text text-muted">إضافة صور جديدة</p>
                <a href="/admin/upload" class="btn btn-outline-info">
                    <i class="fas fa-plus me-2"></i>رفع صور
                </a>
            </div>
        </div>
    </div>
</div>

<?php if (!empty($recent_images)): ?>
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="fas fa-clock me-2"></i>الصور المضافة حديثاً
        </h5>
        <a href="/admin/images" class="btn btn-sm btn-outline-primary">
            عرض الكل
        </a>
    </div>
    <div class="card-body">
        <div class="row">
            <?php foreach ($recent_images as $image): ?>
                <div class="col-md-2 col-sm-4 col-6 mb-3">
                    <div class="card">
                        <img src="/uploads/thumbnails/<?= htmlspecialchars($image['thumb_path']) ?>" 
                             class="card-img-top" 
                             alt="<?= htmlspecialchars($image['alt_text']) ?>"
                             style="height: 120px; object-fit: cover;">
                        <div class="card-body p-2">
                            <h6 class="card-title small mb-1" title="<?= htmlspecialchars($image['title']) ?>">
                                <?= htmlspecialchars(mb_substr($image['title'], 0, 20)) ?>
                                <?= mb_strlen($image['title']) > 20 ? '...' : '' ?>
                            </h6>
                            <small class="text-muted">
                                <?= date('Y/m/d', strtotime($image['created_at'])) ?>
                            </small>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
</div>
<?php endif; ?>

<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-line me-2"></i>إحصائيات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>متوسط الصور لكل صنف</span>
                    <span class="badge bg-primary">
                        <?= $total_categories > 0 ? round($total_images / $total_categories, 1) : 0 ?>
                    </span>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>الصور المضافة اليوم</span>
                    <span class="badge bg-success">
                        <?php
                        $today = date('Y-m-d');
                        $todayCount = 0;
                        foreach ($recent_images as $image) {
                            if (date('Y-m-d', strtotime($image['created_at'])) === $today) {
                                $todayCount++;
                            }
                        }
                        echo $todayCount;
                        ?>
                    </span>
                </div>
                <div class="d-flex justify-content-between align-items-center">
                    <span>الصور المضافة هذا الأسبوع</span>
                    <span class="badge bg-info">
                        <?php
                        $weekAgo = date('Y-m-d', strtotime('-7 days'));
                        $weekCount = 0;
                        foreach ($recent_images as $image) {
                            if (date('Y-m-d', strtotime($image['created_at'])) >= $weekAgo) {
                                $weekCount++;
                            }
                        }
                        echo $weekCount;
                        ?>
                    </span>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-tools me-2"></i>إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="/admin/categories?action=create" class="btn btn-outline-primary">
                        <i class="fas fa-plus me-2"></i>إضافة صنف جديد
                    </a>
                    <a href="/admin/upload" class="btn btn-outline-success">
                        <i class="fas fa-cloud-upload-alt me-2"></i>رفع صور جديدة
                    </a>
                    <a href="/" target="_blank" class="btn btn-outline-info">
                        <i class="fas fa-external-link-alt me-2"></i>معاينة المعرض
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
$content = ob_get_clean();

// Include admin layout
$currentPage = 'dashboard';
$pageTitle = 'الرئيسية';
include VIEWS_PATH . 'layouts/admin.php';
?>
