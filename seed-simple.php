<?php

// Use simple bootstrap if Composer is not available
if (file_exists(__DIR__ . '/vendor/autoload.php')) {
    require_once __DIR__ . '/vendor/autoload.php';
    require_once __DIR__ . '/src/config.php';
    require_once __DIR__ . '/database/seeders/DatabaseSeeder.php';
} else {
    echo "Composer not found, using simple bootstrap...\n";
    require_once __DIR__ . '/bootstrap.php';
    
    // Load environment variables
    $dotenv = Dotenv::createImmutable(__DIR__);
    $dotenv->load();
    
    // Database configuration
    define('DB_HOST', $_ENV['DB_HOST'] ?? 'localhost');
    define('DB_PORT', $_ENV['DB_PORT'] ?? '3306');
    define('DB_DATABASE', $_ENV['DB_DATABASE'] ?? 'atv_gallery');
    define('DB_USERNAME', $_ENV['DB_USERNAME'] ?? 'root');
    define('DB_PASSWORD', $_ENV['DB_PASSWORD'] ?? '');
    
    // Database connection class
    class Database {
        private static $connection = null;
        
        public static function getConnection() {
            if (self::$connection === null) {
                try {
                    $dsn = "mysql:host=" . DB_HOST . ";port=" . DB_PORT . ";dbname=" . DB_DATABASE . ";charset=utf8mb4";
                    self::$connection = new PDO($dsn, DB_USERNAME, DB_PASSWORD, [
                        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                        PDO::ATTR_EMULATE_PREPARES => false,
                    ]);
                } catch (PDOException $e) {
                    die("Database connection failed: " . $e->getMessage() . "\n");
                }
            }
            return self::$connection;
        }
    }
    
    // Simple DatabaseSeeder class
    class DatabaseSeeder {
        private $pdo;
        
        public function __construct() {
            $this->pdo = Database::getConnection();
        }
        
        public function run() {
            echo "Starting database seeding...\n";
            
            $this->seedUsers();
            $this->seedCategories();
            
            echo "Database seeding completed successfully!\n";
        }
        
        private function seedUsers() {
            echo "Seeding users...\n";
            
            // Check if admin user already exists
            $stmt = $this->pdo->prepare("SELECT COUNT(*) FROM users WHERE email = ?");
            $stmt->execute(['<EMAIL>']);
            
            if ($stmt->fetchColumn() == 0) {
                // Create admin user
                $stmt = $this->pdo->prepare("
                    INSERT INTO users (email, password, name, is_admin, created_at, updated_at) 
                    VALUES (?, ?, ?, ?, NOW(), NOW())
                ");
                
                $hashedPassword = password_hash('admin123', PASSWORD_DEFAULT);
                $stmt->execute([
                    '<EMAIL>',
                    $hashedPassword,
                    'مدير النظام',
                    1
                ]);
                
                echo "Admin user created: <EMAIL> / admin123\n";
            } else {
                echo "Admin user already exists.\n";
            }
        }
        
        private function seedCategories() {
            echo "Seeding categories...\n";
            
            $categories = [
                [
                    'name' => 'طبيعة',
                    'slug' => 'nature',
                    'description' => 'صور طبيعية خلابة من مناظر طبيعية وحدائق ومناظر جبلية'
                ],
                [
                    'name' => 'مدن',
                    'slug' => 'cities',
                    'description' => 'صور من المدن والمباني والعمارة الحديثة والتراثية'
                ],
                [
                    'name' => 'أشخاص',
                    'slug' => 'people',
                    'description' => 'صور شخصية وجماعية ولقطات من الحياة اليومية'
                ],
                [
                    'name' => 'طعام',
                    'slug' => 'food',
                    'description' => 'صور للأطعمة والمشروبات والوصفات'
                ],
                [
                    'name' => 'تقنية',
                    'slug' => 'technology',
                    'description' => 'صور للأجهزة التقنية والابتكارات والتطبيقات'
                ]
            ];
            
            foreach ($categories as $category) {
                // Check if category already exists
                $stmt = $this->pdo->prepare("SELECT COUNT(*) FROM categories WHERE slug = ?");
                $stmt->execute([$category['slug']]);
                
                if ($stmt->fetchColumn() == 0) {
                    $stmt = $this->pdo->prepare("
                        INSERT INTO categories (name, slug, description, created_at, updated_at) 
                        VALUES (?, ?, ?, NOW(), NOW())
                    ");
                    
                    $stmt->execute([
                        $category['name'],
                        $category['slug'],
                        $category['description']
                    ]);
                    
                    echo "Category created: {$category['name']}\n";
                } else {
                    echo "Category already exists: {$category['name']}\n";
                }
            }
        }
    }
}

if (php_sapi_name() !== 'cli') {
    die('This script can only be run from the command line.');
}

try {
    $seeder = new DatabaseSeeder();
    $seeder->run();
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    exit(1);
}
